{"name": "spin-to-win-backend", "version": "1.0.0", "description": "Backend API for Spin-to-Win dApp", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "build": "echo 'No build step required'", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "clean": "rm -rf node_modules dist"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "mongoose": "^8.0.3", "redis": "^4.6.10", "ethers": "^5.7.2", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "joi": "^17.11.0", "winston": "^3.11.0", "express-rate-limit": "^7.1.5", "compression": "^1.7.4"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "eslint": "^8.55.0", "eslint-config-node": "^4.1.0", "eslint-plugin-node": "^11.1.0"}, "engines": {"node": ">=18.0.0"}}