const express = require('express');
const Spin = require('../models/Spin');
const contractService = require('../services/contractService');
const logger = require('../utils/logger');

const router = express.Router();

/**
 * GET /api/stats
 * Get global game statistics
 */
router.get('/', async (req, res) => {
  try {
    // Get stats from database and contract
    const [dbStats, contractStats] = await Promise.all([
      Spin.getGlobalStats(),
      contractService.getContractStats()
    ]);

    res.json({
      success: true,
      data: {
        // Database stats
        totalSpins: dbStats.totalSpins,
        totalVolume: dbStats.totalVolume,
        totalVolumeUSD: dbStats.totalVolume / 1e6,
        totalPayouts: dbStats.totalPayouts,
        totalPayoutsUSD: dbStats.totalPayouts / 1e6,
        uniquePlayers: dbStats.uniquePlayers,
        wins: dbStats.wins,
        winRate: dbStats.totalSpins > 0 ? (dbStats.wins / dbStats.totalSpins) * 100 : 0,
        houseEdge: dbStats.houseEdge,
        
        // Contract stats
        contractBalance: contractStats.contractBalance,
        contractBalanceUSD: parseFloat(contractStats.contractBalance) / 1e6,
        minBet: contractStats.minBet,
        minBetUSD: parseFloat(contractStats.minBet) / 1e6,
        maxBet: contractStats.maxBet,
        maxBetUSD: parseFloat(contractStats.maxBet) / 1e6,
        
        // Wheel configuration
        wheelMultipliers: contractStats.wheelMultipliers,
        wheelOdds: contractStats.wheelOdds
      }
    });

  } catch (error) {
    logger.error('Error getting global stats:', error);
    res.status(500).json({
      error: 'Internal server error',
      details: 'Failed to get global statistics'
    });
  }
});

/**
 * GET /api/stats/recent-spins
 * Get recent spins across all players
 */
router.get('/recent-spins', async (req, res) => {
  try {
    const limit = Math.min(parseInt(req.query.limit) || 20, 100);

    const recentSpins = await Spin.find({ isResolved: true })
      .sort({ resolvedAt: -1 })
      .limit(limit)
      .select('playerAddress betAmountUSD multiplier payoutUSD resolvedAt transactionHash')
      .lean();

    res.json({
      success: true,
      data: recentSpins.map(spin => ({
        playerAddress: `${spin.playerAddress.slice(0, 6)}...${spin.playerAddress.slice(-4)}`,
        betAmountUSD: spin.betAmountUSD,
        multiplier: spin.multiplier,
        payoutUSD: spin.payoutUSD,
        profit: spin.payoutUSD - spin.betAmountUSD,
        isWin: spin.multiplier > 0,
        resolvedAt: spin.resolvedAt,
        transactionHash: spin.transactionHash
      }))
    });

  } catch (error) {
    logger.error('Error getting recent spins:', error);
    res.status(500).json({
      error: 'Internal server error',
      details: 'Failed to get recent spins'
    });
  }
});

/**
 * GET /api/stats/big-wins
 * Get biggest wins in the last 24 hours
 */
router.get('/big-wins', async (req, res) => {
  try {
    const limit = Math.min(parseInt(req.query.limit) || 10, 50);
    const hours = Math.min(parseInt(req.query.hours) || 24, 168); // Max 1 week

    const since = new Date(Date.now() - hours * 60 * 60 * 1000);

    const bigWins = await Spin.find({
      isResolved: true,
      multiplier: { $gt: 0 },
      resolvedAt: { $gte: since }
    })
    .sort({ payoutUSD: -1 })
    .limit(limit)
    .select('playerAddress betAmountUSD multiplier payoutUSD resolvedAt transactionHash')
    .lean();

    res.json({
      success: true,
      data: bigWins.map(spin => ({
        playerAddress: `${spin.playerAddress.slice(0, 6)}...${spin.playerAddress.slice(-4)}`,
        betAmountUSD: spin.betAmountUSD,
        multiplier: spin.multiplier,
        payoutUSD: spin.payoutUSD,
        profit: spin.payoutUSD - spin.betAmountUSD,
        resolvedAt: spin.resolvedAt,
        transactionHash: spin.transactionHash
      }))
    });

  } catch (error) {
    logger.error('Error getting big wins:', error);
    res.status(500).json({
      error: 'Internal server error',
      details: 'Failed to get big wins'
    });
  }
});

/**
 * GET /api/stats/leaderboard
 * Get top players by profit
 */
router.get('/leaderboard', async (req, res) => {
  try {
    const limit = Math.min(parseInt(req.query.limit) || 10, 100);
    const period = req.query.period || 'all'; // all, week, month

    let matchStage = { isResolved: true };
    
    if (period === 'week') {
      const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
      matchStage.resolvedAt = { $gte: weekAgo };
    } else if (period === 'month') {
      const monthAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      matchStage.resolvedAt = { $gte: monthAgo };
    }

    const leaderboard = await Spin.aggregate([
      { $match: matchStage },
      {
        $group: {
          _id: '$playerAddress',
          totalSpins: { $sum: 1 },
          totalBet: { $sum: '$betAmountUSD' },
          totalPayout: { $sum: '$payoutUSD' },
          wins: { $sum: { $cond: [{ $gt: ['$multiplier', 0] }, 1, 0] } },
          biggestWin: { $max: '$payoutUSD' }
        }
      },
      {
        $addFields: {
          profit: { $subtract: ['$totalPayout', '$totalBet'] },
          winRate: { $multiply: [{ $divide: ['$wins', '$totalSpins'] }, 100] }
        }
      },
      { $sort: { profit: -1 } },
      { $limit: limit }
    ]);

    res.json({
      success: true,
      data: leaderboard.map((player, index) => ({
        rank: index + 1,
        playerAddress: `${player._id.slice(0, 6)}...${player._id.slice(-4)}`,
        totalSpins: player.totalSpins,
        totalBet: player.totalBet,
        totalPayout: player.totalPayout,
        profit: player.profit,
        winRate: player.winRate,
        biggestWin: player.biggestWin
      }))
    });

  } catch (error) {
    logger.error('Error getting leaderboard:', error);
    res.status(500).json({
      error: 'Internal server error',
      details: 'Failed to get leaderboard'
    });
  }
});

/**
 * GET /api/stats/wheel-distribution
 * Get distribution of wheel results
 */
router.get('/wheel-distribution', async (req, res) => {
  try {
    const hours = Math.min(parseInt(req.query.hours) || 24, 168); // Max 1 week
    const since = new Date(Date.now() - hours * 60 * 60 * 1000);

    const distribution = await Spin.aggregate([
      {
        $match: {
          isResolved: true,
          resolvedAt: { $gte: since }
        }
      },
      {
        $group: {
          _id: '$segmentIndex',
          count: { $sum: 1 },
          totalBet: { $sum: '$betAmountUSD' },
          totalPayout: { $sum: '$payoutUSD' }
        }
      },
      { $sort: { _id: 1 } }
    ]);

    // Get wheel configuration
    const contractStats = await contractService.getContractStats();
    
    // Map results to wheel segments
    const wheelData = contractStats.wheelMultipliers.map((multiplier, index) => {
      const segmentData = distribution.find(d => d._id === index) || { count: 0, totalBet: 0, totalPayout: 0 };
      return {
        segmentIndex: index,
        multiplier,
        expectedOdds: contractStats.wheelOdds[index] / 100, // Convert to percentage
        actualCount: segmentData.count,
        totalBet: segmentData.totalBet,
        totalPayout: segmentData.totalPayout
      };
    });

    const totalSpins = distribution.reduce((sum, d) => sum + d.count, 0);

    res.json({
      success: true,
      data: {
        totalSpins,
        period: `${hours} hours`,
        segments: wheelData.map(segment => ({
          ...segment,
          actualOdds: totalSpins > 0 ? (segment.actualCount / totalSpins) * 100 : 0
        }))
      }
    });

  } catch (error) {
    logger.error('Error getting wheel distribution:', error);
    res.status(500).json({
      error: 'Internal server error',
      details: 'Failed to get wheel distribution'
    });
  }
});

module.exports = router;
