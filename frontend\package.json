{"name": "spin-to-win-frontend", "version": "1.0.0", "description": "Frontend for Spin-to-Win dApp", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.1", "@web3modal/ethereum": "^2.7.1", "@web3modal/react": "^2.7.1", "ethers": "^5.7.2", "framer-motion": "^10.16.16", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hot-toast": "^2.4.1", "react-router-dom": "^6.20.1", "react-scripts": "5.0.1", "recharts": "^2.8.0", "styled-components": "^6.1.6", "wagmi": "^1.4.12", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "clean": "rm -rf node_modules build"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"eslint": "^8.55.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0"}, "proxy": "http://localhost:3001"}