const request = require('supertest');
const app = require('../index');
const Spin = require('../models/Spin');

// Mock the contract service
jest.mock('../services/contractService', () => ({
  validateBetAmount: jest.fn(),
  getPlayerBalance: jest.fn(),
  getPlayerAllowance: jest.fn(),
  initialize: jest.fn(),
}));

describe('Spin API Endpoints', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('POST /api/spin', () => {
    const validBetData = {
      amount: '2000000', // 2 USDT in wei (6 decimals)
      playerAddress: '0x1234567890123456789012345678901234567890'
    };

    it('should validate bet amount successfully', async () => {
      const contractService = require('../services/contractService');
      contractService.validateBetAmount.mockResolvedValue({ valid: true });
      contractService.getPlayerBalance.mockResolvedValue('10000000'); // 10 USDT
      contractService.getPlayerAllowance.mockResolvedValue('5000000'); // 5 USDT

      const response = await request(app)
        .post('/api/spin')
        .send(validBetData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toContain('Validation passed');
    });

    it('should reject invalid bet amount', async () => {
      const contractService = require('../services/contractService');
      contractService.validateBetAmount.mockResolvedValue({ 
        valid: false, 
        error: 'Bet amount too low' 
      });

      const response = await request(app)
        .post('/api/spin')
        .send({ ...validBetData, amount: '1000000' }) // 1 USDT
        .expect(400);

      expect(response.body.error).toBe('Invalid bet amount');
    });

    it('should reject insufficient balance', async () => {
      const contractService = require('../services/contractService');
      contractService.validateBetAmount.mockResolvedValue({ valid: true });
      contractService.getPlayerBalance.mockResolvedValue('1000000'); // 1 USDT
      contractService.getPlayerAllowance.mockResolvedValue('5000000'); // 5 USDT

      const response = await request(app)
        .post('/api/spin')
        .send(validBetData)
        .expect(400);

      expect(response.body.error).toBe('Insufficient balance');
    });

    it('should reject insufficient allowance', async () => {
      const contractService = require('../services/contractService');
      contractService.validateBetAmount.mockResolvedValue({ valid: true });
      contractService.getPlayerBalance.mockResolvedValue('10000000'); // 10 USDT
      contractService.getPlayerAllowance.mockResolvedValue('1000000'); // 1 USDT

      const response = await request(app)
        .post('/api/spin')
        .send(validBetData)
        .expect(400);

      expect(response.body.error).toBe('Insufficient allowance');
    });

    it('should validate input format', async () => {
      const response = await request(app)
        .post('/api/spin')
        .send({
          amount: 'invalid',
          playerAddress: 'invalid-address'
        })
        .expect(400);

      expect(response.body.error).toBe('Validation error');
    });
  });

  describe('GET /api/spin/result/:txHash', () => {
    it('should return spin result for valid transaction', async () => {
      const mockSpin = {
        requestId: '1',
        playerAddress: '0x1234567890123456789012345678901234567890',
        betAmount: '2000000',
        betAmountUSD: 2,
        isResolved: true,
        segmentIndex: 2,
        multiplier: 2,
        payout: '4000000',
        payoutUSD: 4,
        profitLoss: 2,
        isWin: true,
        placedAt: new Date(),
        resolvedAt: new Date(),
        transactionHash: '0x1234567890123456789012345678901234567890123456789012345678901234',
        blockNumber: 12345
      };

      jest.spyOn(Spin, 'findOne').mockResolvedValue(mockSpin);

      const response = await request(app)
        .get('/api/spin/result/0x1234567890123456789012345678901234567890123456789012345678901234')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.isResolved).toBe(true);
      expect(response.body.data.multiplier).toBe(2);
    });

    it('should return 404 for non-existent transaction', async () => {
      jest.spyOn(Spin, 'findOne').mockResolvedValue(null);

      const response = await request(app)
        .get('/api/spin/result/0x1234567890123456789012345678901234567890123456789012345678901234')
        .expect(404);

      expect(response.body.error).toBe('Spin not found');
    });

    it('should validate transaction hash format', async () => {
      const response = await request(app)
        .get('/api/spin/result/invalid-hash')
        .expect(400);

      expect(response.body.error).toBe('Validation error');
    });
  });

  describe('GET /api/spin/pending/:address', () => {
    it('should return pending spins for valid address', async () => {
      const mockPendingSpins = [
        {
          requestId: '1',
          betAmount: '2000000',
          betAmountUSD: 2,
          placedAt: new Date(),
          transactionHash: '0x1234567890123456789012345678901234567890123456789012345678901234'
        }
      ];

      jest.spyOn(Spin, 'find').mockReturnValue({
        sort: jest.fn().mockResolvedValue(mockPendingSpins)
      });

      const response = await request(app)
        .get('/api/spin/pending/0x1234567890123456789012345678901234567890')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveLength(1);
    });

    it('should validate address format', async () => {
      const response = await request(app)
        .get('/api/spin/pending/invalid-address')
        .expect(400);

      expect(response.body.error).toBe('Invalid address format');
    });
  });

  describe('GET /api/spin/validate/:amount', () => {
    it('should validate amount successfully', async () => {
      const contractService = require('../services/contractService');
      contractService.validateBetAmount.mockResolvedValue({ valid: true });
      contractService.getContractStats.mockResolvedValue({
        minBet: '2000000',
        maxBet: '100000000'
      });

      const response = await request(app)
        .get('/api/spin/validate/2000000')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.valid).toBe(true);
    });

    it('should return validation error for invalid amount', async () => {
      const contractService = require('../services/contractService');
      contractService.validateBetAmount.mockResolvedValue({ 
        valid: false, 
        error: 'Amount too low' 
      });

      const response = await request(app)
        .get('/api/spin/validate/1000000')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.valid).toBe(false);
      expect(response.body.error).toBe('Amount too low');
    });

    it('should reject invalid amount format', async () => {
      const response = await request(app)
        .get('/api/spin/validate/invalid')
        .expect(400);

      expect(response.body.error).toBe('Invalid amount format');
    });
  });
});
