# Spin-to-Win dApp

A provably fair Web3 gambling application where users can spin a prize wheel using USDT tokens.

## Features

- 🎰 Spin-to-win mechanism with USDT betting
- 🔗 Chainlink VRF for provably fair randomness
- 💰 Prize multipliers: Lose, 1×, 2×, 5×, 10× bet
- 🔐 Secure wallet connection (MetaMask, WalletConnect)
- 📊 Real-time balance tracking and spin history
- 🎨 Animated wheel interface

## Tech Stack

- **Smart Contracts**: Solidity, Hardhat, OpenZeppelin, Chainlink VRF
- **Backend**: Node.js, Express, MongoDB, Ethers.js
- **Frontend**: React, Web3Modal, Framer Motion
- **DevOps**: Docker, GitHub Actions

## Project Structure

```
spin-to-win-dapp/
├── contracts/              # Smart contracts
│   ├── contracts/
│   ├── scripts/
│   ├── test/
│   └── hardhat.config.js
├── backend/                # API server
│   ├── src/
│   ├── models/
│   └── package.json
├── frontend/               # React app
│   ├── src/
│   ├── public/
│   └── package.json
├── docker/                 # Docker configs
├── .github/workflows/      # CI/CD
└── docs/                   # Documentation
```

## Quick Start

### Prerequisites

- Node.js 18+
- Docker and Docker Compose
- MetaMask wallet
- Testnet ETH and USDT tokens

### 🚀 One-Command Setup

```bash
# Clone the repository
git clone <repo-url>
cd spin-to-win-dapp

# Quick setup with Makefile
make quick-start

# Or manual setup
npm run install:all
cp .env.example .env
# Edit .env with your configuration
```

### 🔧 Development Setup

1. **Start database services**:
```bash
make dev-db
# This starts MongoDB, Redis, and admin UIs
```

2. **Deploy contracts** (in a new terminal):
```bash
cd contracts
npm run deploy:testnet
```

3. **Start backend** (in a new terminal):
```bash
cd backend
npm run dev
```

4. **Start frontend** (in a new terminal):
```bash
cd frontend
npm start
```

### 🌐 Access the Application

- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:3001
- **MongoDB Admin**: http://localhost:8081 (admin/admin)
- **Redis Admin**: http://localhost:8082

## Environment Variables

Copy `.env.example` to `.env` and configure:

- `PRIVATE_KEY`: Deployer wallet private key
- `RPC_URL_MAINNET`: Ethereum/BSC mainnet RPC
- `RPC_URL_TESTNET`: Testnet RPC
- `CHAINLINK_VRF_COORDINATOR`: VRF coordinator address
- `CHAINLINK_KEY_HASH`: VRF key hash
- `USDT_CONTRACT_ADDRESS`: USDT token contract
- `MONGO_URI`: MongoDB connection string
- `JWT_SECRET`: JWT signing secret

## Deployment

### Testnet
```bash
npm run deploy:testnet
```

### Mainnet
```bash
npm run deploy:mainnet
```

## Testing

```bash
# Smart contracts
cd contracts
npm test

# Backend
cd backend
npm test

# Frontend
cd frontend
npm test
```

## Architecture

### Smart Contracts
- **SpinToWin.sol**: Main game contract with Chainlink VRF integration
- **Provably Fair**: Uses Chainlink VRF for verifiable randomness
- **Security**: OpenZeppelin contracts, reentrancy protection, pausable

### Backend API
- **Express.js**: RESTful API server
- **MongoDB**: Spin history and user statistics
- **Redis**: Caching and session management
- **Web3 Integration**: Real-time event listening and contract interaction

### Frontend
- **React**: Modern UI with hooks and context
- **Web3Modal**: Multi-wallet connection support
- **Framer Motion**: Smooth animations for wheel spinning
- **Responsive Design**: Mobile-first approach

## API Endpoints

### Spin Endpoints
- `POST /api/spin` - Validate and place a bet
- `GET /api/spin/result/:txHash` - Get spin result by transaction hash
- `GET /api/spin/pending/:address` - Get pending spins for user
- `GET /api/spin/validate/:amount` - Validate bet amount

### User Endpoints
- `GET /api/user/history/:address` - Get user spin history with pagination
- `GET /api/user/stats/:address` - Get user statistics
- `GET /api/user/balance/:address` - Get user USDT balance and allowance
- `GET /api/user/recent-wins/:address` - Get user's recent wins

### Statistics Endpoints
- `GET /api/stats` - Get global game statistics
- `GET /api/stats/recent-spins` - Get recent spins from all players
- `GET /api/stats/big-wins` - Get biggest wins in timeframe
- `GET /api/stats/leaderboard` - Get top players by profit
- `GET /api/stats/wheel-distribution` - Get wheel result distribution

## Smart Contract Functions

### Player Functions
```solidity
function placeBet(uint256 amount) external
function getSpin(uint256 requestId) external view returns (Spin memory)
function getPlayerSpins(address player) external view returns (uint256[] memory)
```

### View Functions
```solidity
function getContractBalance() external view returns (uint256)
function getMaxBet() external view returns (uint256)
function getWheelConfig() external view returns (uint256[] memory, uint256[] memory)
```

### Owner Functions
```solidity
function setWheelSegments(uint256[] calldata multipliers, uint256[] calldata odds) external onlyOwner
function withdrawTreasury(address to, uint256 amount) external onlyOwner
function pause() external onlyOwner
function unpause() external onlyOwner
```

## Development Setup

### Prerequisites
- Node.js 18+
- MongoDB 5+
- Redis 6+
- MetaMask or compatible Web3 wallet
- Testnet ETH and USDT tokens

### Local Development
1. **Clone and install**:
```bash
git clone <repository-url>
cd spin-to-win-dapp
npm run install:all
```

2. **Set up environment**:
```bash
cp .env.example .env
# Edit .env with your configuration
```

3. **Start local blockchain** (optional):
```bash
cd contracts
npx hardhat node
```

4. **Deploy contracts**:
```bash
cd contracts
npm run deploy:testnet
```

5. **Start services**:
```bash
# Terminal 1: Start MongoDB and Redis
docker-compose up mongodb redis

# Terminal 2: Start backend
cd backend
npm run dev

# Terminal 3: Start frontend
cd frontend
npm start
```

### Docker Development
```bash
# Start all services
docker-compose up

# Start specific services
docker-compose up mongodb redis backend

# View logs
docker-compose logs -f backend
```

## Production Deployment

### Using Docker Compose
```bash
# Production deployment
docker-compose --profile production up -d

# Update services
docker-compose pull
docker-compose up -d
```

### Manual Deployment
1. **Deploy contracts to mainnet**:
```bash
cd contracts
npm run deploy:mainnet
```

2. **Build and deploy backend**:
```bash
cd backend
npm run build
npm start
```

3. **Build and deploy frontend**:
```bash
cd frontend
npm run build
# Serve build/ directory with nginx or similar
```

## Testing

### Smart Contracts
```bash
cd contracts
npm test
npm run coverage
```

### Backend
```bash
cd backend
npm test
npm run test:watch
```

### Frontend
```bash
cd frontend
npm test
npm run test:coverage
```

### Integration Tests
```bash
# Run all tests
npm test
```

## Security Considerations

### Smart Contract Security
- ✅ Reentrancy protection with OpenZeppelin's ReentrancyGuard
- ✅ Access control with Ownable pattern
- ✅ Pausable functionality for emergency stops
- ✅ Input validation and bounds checking
- ✅ Safe math operations (Solidity 0.8+)
- ✅ Chainlink VRF for provably fair randomness

### Backend Security
- ✅ Rate limiting on API endpoints
- ✅ Input validation with Joi
- ✅ CORS configuration
- ✅ Helmet.js for security headers
- ✅ Environment variable protection
- ✅ Error handling without information leakage

### Frontend Security
- ✅ Content Security Policy headers
- ✅ XSS protection
- ✅ Secure wallet connection handling
- ✅ Input sanitization
- ✅ HTTPS enforcement in production

## Monitoring and Logging

### Application Monitoring
- Winston logging with multiple transports
- Health check endpoints
- Error tracking and alerting
- Performance metrics

### Blockchain Monitoring
- Contract event listening
- Transaction status tracking
- Gas usage optimization
- Network status monitoring

## Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit changes: `git commit -m 'Add amazing feature'`
4. Push to branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

### Development Guidelines
- Follow existing code style and conventions
- Write tests for new features
- Update documentation as needed
- Ensure all CI checks pass

## License

MIT License - see [LICENSE](LICENSE) file for details

## Disclaimer

⚠️ **Important**: This is a gambling application. Please gamble responsibly and only bet what you can afford to lose. Users must be 18+ and comply with local laws and regulations. The developers are not responsible for any losses incurred while using this application.
