import React, { useState, useEffect } from 'react';
import { useGame } from '../contexts/GameContext';
import { formatAddress, getExplorerUrl } from '../config/contracts';
import { WHEEL_SEGMENTS } from '../config/contracts';

const RecentSpins = () => {
  const { recentSpins, fetchRecentSpins } = useGame();
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadRecentSpins = async () => {
      setIsLoading(true);
      await fetchRecentSpins(10);
      setIsLoading(false);
    };

    loadRecentSpins();
  }, []);

  const getSegmentInfo = (multiplier) => {
    const segment = WHEEL_SEGMENTS.find(s => s.multiplier === multiplier);
    return segment || { label: 'Unknown', color: '#666' };
  };

  const formatTimeAgo = (timestamp) => {
    const now = new Date();
    const time = new Date(timestamp);
    const diffInSeconds = Math.floor((now - time) / 1000);

    if (diffInSeconds < 60) {
      return `${diffInSeconds}s ago`;
    } else if (diffInSeconds < 3600) {
      return `${Math.floor(diffInSeconds / 60)}m ago`;
    } else if (diffInSeconds < 86400) {
      return `${Math.floor(diffInSeconds / 3600)}h ago`;
    } else {
      return `${Math.floor(diffInSeconds / 86400)}d ago`;
    }
  };

  return (
    <div className="card">
      <div className="card-header">
        <h3 className="card-title">🔥 Recent Spins</h3>
        <p className="card-subtitle">Live results from all players</p>
      </div>

      {isLoading ? (
        <div style={{ display: 'flex', flexDirection: 'column', gap: '0.75rem' }}>
          {[...Array(5)].map((_, i) => (
            <div key={i} className="skeleton" style={{ height: '60px', borderRadius: '0.5rem' }} />
          ))}
        </div>
      ) : recentSpins.length > 0 ? (
        <div style={{ 
          display: 'flex', 
          flexDirection: 'column', 
          gap: '0.75rem',
          maxHeight: '400px',
          overflowY: 'auto'
        }}>
          {recentSpins.map((spin, index) => {
            const segment = getSegmentInfo(spin.multiplier);
            const isWin = spin.multiplier > 0;
            
            return (
              <div
                key={index}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.75rem',
                  padding: '0.75rem',
                  background: 'rgba(255, 255, 255, 0.05)',
                  borderRadius: '0.5rem',
                  border: `1px solid ${isWin ? 'rgba(16, 185, 129, 0.3)' : 'rgba(239, 68, 68, 0.3)'}`
                }}
              >
                {/* Result indicator */}
                <div
                  style={{
                    width: '12px',
                    height: '12px',
                    borderRadius: '50%',
                    background: segment.color,
                    flexShrink: 0
                  }}
                />

                {/* Spin info */}
                <div style={{ flex: 1, minWidth: 0 }}>
                  <div style={{ 
                    display: 'flex', 
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    marginBottom: '0.25rem'
                  }}>
                    <span style={{ 
                      fontSize: '0.875rem',
                      fontWeight: '500',
                      color: isWin ? '#10b981' : '#ef4444'
                    }}>
                      {segment.label}
                    </span>
                    <span style={{ 
                      fontSize: '0.75rem',
                      opacity: 0.6
                    }}>
                      {formatTimeAgo(spin.resolvedAt)}
                    </span>
                  </div>
                  
                  <div style={{ 
                    display: 'flex', 
                    justifyContent: 'space-between',
                    fontSize: '0.75rem',
                    opacity: 0.8
                  }}>
                    <span>{formatAddress(spin.playerAddress, 4, 4)}</span>
                    <span>
                      ${spin.betAmountUSD.toFixed(2)} → ${spin.payoutUSD.toFixed(2)}
                    </span>
                  </div>
                  
                  {isWin && (
                    <div style={{
                      fontSize: '0.75rem',
                      color: '#10b981',
                      fontWeight: '500',
                      marginTop: '0.25rem'
                    }}>
                      +${spin.profit.toFixed(2)} profit
                    </div>
                  )}
                </div>

                {/* External link */}
                <a
                  href={getExplorerUrl(1, spin.transactionHash)} // Assuming mainnet for now
                  target="_blank"
                  rel="noopener noreferrer"
                  style={{
                    color: 'rgba(255, 255, 255, 0.6)',
                    textDecoration: 'none',
                    fontSize: '0.75rem',
                    flexShrink: 0
                  }}
                  title="View on explorer"
                >
                  ↗
                </a>
              </div>
            );
          })}
        </div>
      ) : (
        <div style={{
          textAlign: 'center',
          padding: '2rem',
          opacity: 0.6
        }}>
          <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>🎰</div>
          <p>No recent spins yet</p>
          <p style={{ fontSize: '0.875rem' }}>Be the first to spin!</p>
        </div>
      )}

      {/* Refresh button */}
      <div style={{ 
        marginTop: '1rem',
        paddingTop: '1rem',
        borderTop: '1px solid rgba(255, 255, 255, 0.1)'
      }}>
        <button
          onClick={() => fetchRecentSpins(10)}
          className="btn btn-sm btn-secondary"
          style={{ width: '100%' }}
          disabled={isLoading}
        >
          {isLoading ? (
            <>
              <div className="loading-spinner" />
              Loading...
            </>
          ) : (
            'Refresh'
          )}
        </button>
      </div>
    </div>
  );
};

export default RecentSpins;
