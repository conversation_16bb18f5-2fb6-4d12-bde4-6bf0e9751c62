{"name": "spin-to-win-contracts", "version": "1.0.0", "description": "Smart contracts for Spin-to-Win dApp", "scripts": {"compile": "hardhat compile", "test": "hardhat test", "deploy:testnet": "hardhat run scripts/deploy.js --network testnet", "deploy:mainnet": "hardhat run scripts/deploy.js --network mainnet", "verify": "hardhat verify --network mainnet", "lint": "solhint 'contracts/**/*.sol'", "clean": "hardhat clean", "coverage": "hardhat coverage", "size": "hardhat size-contracts"}, "devDependencies": {"@nomicfoundation/hardhat-toolbox": "^4.0.0", "@openzeppelin/hardhat-upgrades": "^2.4.3", "hardhat": "^2.19.4", "hardhat-contract-sizer": "^2.10.0", "hardhat-gas-reporter": "^1.0.9", "solhint": "^4.1.1", "solidity-coverage": "^0.8.5"}, "dependencies": {"@chainlink/contracts": "^0.8.0", "@openzeppelin/contracts": "^5.0.1", "dotenv": "^16.3.1"}}