// Contract ABIs (simplified for key functions)
export const SPIN_TO_WIN_ABI = [
  "function placeBet(uint256 amount) external",
  "function getSpin(uint256 requestId) external view returns (tuple(address player, uint256 betAmount, uint256 requestId, bool fulfilled, uint256 result, uint256 payout, uint256 timestamp))",
  "function getPlayerSpins(address player) external view returns (uint256[])",
  "function totalSpins() external view returns (uint256)",
  "function totalVolume() external view returns (uint256)",
  "function totalPayouts() external view returns (uint256)",
  "function getContractBalance() external view returns (uint256)",
  "function MINIMUM_BET() external view returns (uint256)",
  "function getMaxBet() external view returns (uint256)",
  "function getWheelConfig() external view returns (uint256[], uint256[])",
  "event BetPlaced(address indexed player, uint256 indexed requestId, uint256 betAmount, uint256 timestamp)",
  "event SpinResult(address indexed player, uint256 indexed requestId, uint256 betAmount, uint256 result, uint256 multiplier, uint256 payout, uint256 timestamp)"
];

export const USDT_ABI = [
  "function balanceOf(address account) external view returns (uint256)",
  "function allowance(address owner, address spender) external view returns (uint256)",
  "function approve(address spender, uint256 amount) external returns (bool)",
  "function decimals() external view returns (uint8)",
  "function symbol() external view returns (string)",
  "function name() external view returns (string)"
];

// Contract addresses by chain ID
export const CONTRACTS = {
  // Ethereum Mainnet
  1: {
    name: 'Ethereum',
    spinToWin: process.env.REACT_APP_CONTRACT_ADDRESS || '******************************************',
    usdt: '******************************************',
    spinToWinAbi: SPIN_TO_WIN_ABI,
    usdtAbi: USDT_ABI,
    explorer: 'https://etherscan.io',
    rpcUrl: 'https://mainnet.infura.io/v3/YOUR_INFURA_KEY'
  },
  
  // Sepolia Testnet
  ********: {
    name: 'Sepolia',
    spinToWin: process.env.REACT_APP_CONTRACT_ADDRESS || '******************************************',
    usdt: '******************************************', // Mock USDT
    spinToWinAbi: SPIN_TO_WIN_ABI,
    usdtAbi: USDT_ABI,
    explorer: 'https://sepolia.etherscan.io',
    rpcUrl: 'https://sepolia.infura.io/v3/YOUR_INFURA_KEY'
  },
  
  // BSC Mainnet
  56: {
    name: 'BSC',
    spinToWin: process.env.REACT_APP_CONTRACT_ADDRESS || '******************************************',
    usdt: '******************************************',
    spinToWinAbi: SPIN_TO_WIN_ABI,
    usdtAbi: USDT_ABI,
    explorer: 'https://bscscan.com',
    rpcUrl: 'https://bsc-dataseed1.binance.org/'
  },
  
  // BSC Testnet
  97: {
    name: 'BSC Testnet',
    spinToWin: process.env.REACT_APP_CONTRACT_ADDRESS || '******************************************',
    usdt: '******************************************', // Mock USDT
    spinToWinAbi: SPIN_TO_WIN_ABI,
    usdtAbi: USDT_ABI,
    explorer: 'https://testnet.bscscan.com',
    rpcUrl: 'https://data-seed-prebsc-1-s1.binance.org:8545/'
  }
};

// Supported chain IDs
export const SUPPORTED_CHAINS = Object.keys(CONTRACTS).map(Number);

// Default chain (use testnet for development)
export const DEFAULT_CHAIN_ID = process.env.NODE_ENV === 'production' ? 1 : ********;

// Wheel configuration
export const WHEEL_SEGMENTS = [
  { index: 0, multiplier: 0, label: 'Lose', color: '#ef4444', odds: 40 },
  { index: 1, multiplier: 1, label: '1×', color: '#f59e0b', odds: 30 },
  { index: 2, multiplier: 2, label: '2×', color: '#10b981', odds: 20 },
  { index: 3, multiplier: 5, label: '5×', color: '#3b82f6', odds: 8 },
  { index: 4, multiplier: 10, label: '10×', color: '#8b5cf6', odds: 2 }
];

// API configuration
export const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:3001';

// Game configuration
export const GAME_CONFIG = {
  minBet: 2, // 2 USDT
  maxBet: 1000, // Will be fetched from contract
  decimals: 6, // USDT decimals
  currency: 'USDT'
};

// Utility functions
export const getContractConfig = (chainId) => {
  return CONTRACTS[chainId] || null;
};

export const getExplorerUrl = (chainId, hash, type = 'tx') => {
  const config = getContractConfig(chainId);
  if (!config) return '#';
  
  return `${config.explorer}/${type}/${hash}`;
};

export const formatAddress = (address, start = 6, end = 4) => {
  if (!address) return '';
  return `${address.slice(0, start)}...${address.slice(-end)}`;
};

export const formatAmount = (amount, decimals = 6, precision = 2) => {
  const num = parseFloat(amount) / Math.pow(10, decimals);
  return num.toFixed(precision);
};

export const parseAmount = (amount, decimals = 6) => {
  return (parseFloat(amount) * Math.pow(10, decimals)).toString();
};
