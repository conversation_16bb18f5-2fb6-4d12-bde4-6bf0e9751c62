import React from 'react';

const Footer = () => {
  return (
    <footer style={{
      background: 'rgba(255, 255, 255, 0.1)',
      backdropFilter: 'blur(10px)',
      borderTop: '1px solid rgba(255, 255, 255, 0.2)',
      padding: '2rem 0',
      marginTop: 'auto'
    }}>
      <div className="container">
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
          gap: '2rem',
          marginBottom: '2rem'
        }}>
          {/* About */}
          <div>
            <h4 style={{ marginBottom: '1rem' }}>🎰 Spin-to-Win</h4>
            <p style={{ opacity: 0.8, fontSize: '0.875rem', lineHeight: 1.6 }}>
              A provably fair Web3 gambling dApp powered by Chainlink VRF. 
              Spin the wheel and win up to 10× your bet with transparent, 
              verifiable randomness.
            </p>
          </div>

          {/* Features */}
          <div>
            <h4 style={{ marginBottom: '1rem' }}>Features</h4>
            <ul style={{ 
              listStyle: 'none', 
              padding: 0,
              display: 'flex',
              flexDirection: 'column',
              gap: '0.5rem'
            }}>
              <li style={{ opacity: 0.8, fontSize: '0.875rem' }}>
                ✅ Provably Fair with Chainlink VRF
              </li>
              <li style={{ opacity: 0.8, fontSize: '0.875rem' }}>
                ✅ Instant USDT Payouts
              </li>
              <li style={{ opacity: 0.8, fontSize: '0.875rem' }}>
                ✅ Transparent Smart Contracts
              </li>
              <li style={{ opacity: 0.8, fontSize: '0.875rem' }}>
                ✅ Real-time Statistics
              </li>
            </ul>
          </div>

          {/* Security */}
          <div>
            <h4 style={{ marginBottom: '1rem' }}>Security</h4>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '0.75rem' }}>
              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem',
                fontSize: '0.875rem',
                opacity: 0.8
              }}>
                <span>🔒</span>
                <span>Smart Contract Audited</span>
              </div>
              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem',
                fontSize: '0.875rem',
                opacity: 0.8
              }}>
                <span>🔗</span>
                <span>Chainlink VRF Randomness</span>
              </div>
              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem',
                fontSize: '0.875rem',
                opacity: 0.8
              }}>
                <span>📊</span>
                <span>Open Source Code</span>
              </div>
            </div>
          </div>

          {/* Links */}
          <div>
            <h4 style={{ marginBottom: '1rem' }}>Links</h4>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
              <a
                href="https://github.com/your-repo"
                target="_blank"
                rel="noopener noreferrer"
                style={{
                  color: 'rgba(255, 255, 255, 0.8)',
                  textDecoration: 'none',
                  fontSize: '0.875rem',
                  transition: 'color 0.2s ease'
                }}
                onMouseEnter={(e) => e.target.style.color = '#667eea'}
                onMouseLeave={(e) => e.target.style.color = 'rgba(255, 255, 255, 0.8)'}
              >
                📁 Source Code
              </a>
              <a
                href="https://docs.chain.link/vrf/v2/introduction"
                target="_blank"
                rel="noopener noreferrer"
                style={{
                  color: 'rgba(255, 255, 255, 0.8)',
                  textDecoration: 'none',
                  fontSize: '0.875rem',
                  transition: 'color 0.2s ease'
                }}
                onMouseEnter={(e) => e.target.style.color = '#667eea'}
                onMouseLeave={(e) => e.target.style.color = 'rgba(255, 255, 255, 0.8)'}
              >
                🔗 Chainlink VRF
              </a>
              <a
                href="https://etherscan.io"
                target="_blank"
                rel="noopener noreferrer"
                style={{
                  color: 'rgba(255, 255, 255, 0.8)',
                  textDecoration: 'none',
                  fontSize: '0.875rem',
                  transition: 'color 0.2s ease'
                }}
                onMouseEnter={(e) => e.target.style.color = '#667eea'}
                onMouseLeave={(e) => e.target.style.color = 'rgba(255, 255, 255, 0.8)'}
              >
                📋 Contract Explorer
              </a>
            </div>
          </div>
        </div>

        {/* Bottom section */}
        <div style={{
          borderTop: '1px solid rgba(255, 255, 255, 0.1)',
          paddingTop: '2rem',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          flexWrap: 'wrap',
          gap: '1rem'
        }}>
          <div style={{ opacity: 0.6, fontSize: '0.875rem' }}>
            © 2024 Spin-to-Win dApp. Built with ❤️ for Web3.
          </div>
          
          <div style={{ display: 'flex', gap: '1rem', alignItems: 'center' }}>
            <span style={{ opacity: 0.6, fontSize: '0.875rem' }}>
              Powered by:
            </span>
            <div style={{ display: 'flex', gap: '1rem', alignItems: 'center' }}>
              <span style={{ fontSize: '0.875rem' }}>⚡ Ethereum</span>
              <span style={{ fontSize: '0.875rem' }}>🔗 Chainlink</span>
              <span style={{ fontSize: '0.875rem' }}>💎 USDT</span>
            </div>
          </div>
        </div>

        {/* Disclaimer */}
        <div style={{
          marginTop: '1.5rem',
          padding: '1rem',
          background: 'rgba(239, 68, 68, 0.1)',
          border: '1px solid rgba(239, 68, 68, 0.3)',
          borderRadius: '0.5rem',
          fontSize: '0.75rem',
          opacity: 0.8,
          lineHeight: 1.5
        }}>
          <strong>⚠️ Disclaimer:</strong> This is a gambling application. Please gamble responsibly. 
          Only bet what you can afford to lose. This dApp is for entertainment purposes only. 
          Users must be 18+ and comply with local laws and regulations.
        </div>
      </div>
    </footer>
  );
};

export default Footer;
