const { ethers } = require('ethers');
const logger = require('../utils/logger');
const Spin = require('../models/Spin');

// Contract ABI (simplified for key functions)
const SPIN_TO_WIN_ABI = [
  "function placeBet(uint256 amount) external",
  "function getSpin(uint256 requestId) external view returns (tuple(address player, uint256 betAmount, uint256 requestId, bool fulfilled, uint256 result, uint256 payout, uint256 timestamp))",
  "function getPlayerSpins(address player) external view returns (uint256[])",
  "function totalSpins() external view returns (uint256)",
  "function totalVolume() external view returns (uint256)",
  "function totalPayouts() external view returns (uint256)",
  "function getContractBalance() external view returns (uint256)",
  "function MINIMUM_BET() external view returns (uint256)",
  "function getMaxBet() external view returns (uint256)",
  "function getWheelConfig() external view returns (uint256[], uint256[])",
  "event BetPlaced(address indexed player, uint256 indexed requestId, uint256 betAmount, uint256 timestamp)",
  "event SpinResult(address indexed player, uint256 indexed requestId, uint256 betAmount, uint256 result, uint256 multiplier, uint256 payout, uint256 timestamp)"
];

const USDT_ABI = [
  "function balanceOf(address account) external view returns (uint256)",
  "function allowance(address owner, address spender) external view returns (uint256)",
  "function decimals() external view returns (uint8)"
];

class ContractService {
  constructor() {
    this.provider = null;
    this.spinToWinContract = null;
    this.usdtContract = null;
    this.isInitialized = false;
  }

  initialize() {
    try {
      // Initialize provider
      const rpcUrl = process.env.NODE_ENV === 'production' 
        ? process.env.RPC_URL_MAINNET 
        : process.env.RPC_URL_TESTNET;
      
      if (!rpcUrl) {
        throw new Error('RPC URL not configured');
      }

      this.provider = new ethers.providers.JsonRpcProvider(rpcUrl);

      // Initialize contracts
      const contractAddress = process.env.SPIN_TO_WIN_CONTRACT_ADDRESS;
      const usdtAddress = process.env.USDT_CONTRACT_ADDRESS;

      if (!contractAddress || !usdtAddress) {
        throw new Error('Contract addresses not configured');
      }

      this.spinToWinContract = new ethers.Contract(contractAddress, SPIN_TO_WIN_ABI, this.provider);
      this.usdtContract = new ethers.Contract(usdtAddress, USDT_ABI, this.provider);

      // Set up event listeners
      this.setupEventListeners();

      this.isInitialized = true;
      logger.info('Contract service initialized successfully');

    } catch (error) {
      logger.error('Failed to initialize contract service:', error);
      throw error;
    }
  }

  setupEventListeners() {
    // Listen for BetPlaced events
    this.spinToWinContract.on('BetPlaced', async (player, requestId, betAmount, timestamp, event) => {
      try {
        logger.info(`BetPlaced event: Player ${player}, RequestId ${requestId}, Amount ${betAmount}`);
        
        await this.handleBetPlaced({
          player,
          requestId: requestId.toString(),
          betAmount: betAmount.toString(),
          timestamp: timestamp.toNumber(),
          transactionHash: event.transactionHash,
          blockNumber: event.blockNumber
        });
      } catch (error) {
        logger.error('Error handling BetPlaced event:', error);
      }
    });

    // Listen for SpinResult events
    this.spinToWinContract.on('SpinResult', async (player, requestId, betAmount, result, multiplier, payout, timestamp, event) => {
      try {
        logger.info(`SpinResult event: Player ${player}, RequestId ${requestId}, Result ${result}, Payout ${payout}`);
        
        await this.handleSpinResult({
          player,
          requestId: requestId.toString(),
          betAmount: betAmount.toString(),
          result: result.toNumber(),
          multiplier: multiplier.toNumber(),
          payout: payout.toString(),
          timestamp: timestamp.toNumber(),
          transactionHash: event.transactionHash,
          blockNumber: event.blockNumber
        });
      } catch (error) {
        logger.error('Error handling SpinResult event:', error);
      }
    });

    logger.info('Event listeners set up successfully');
  }

  async handleBetPlaced(eventData) {
    try {
      const spin = new Spin({
        requestId: eventData.requestId,
        transactionHash: eventData.transactionHash,
        blockNumber: eventData.blockNumber,
        playerAddress: eventData.player.toLowerCase(),
        betAmount: eventData.betAmount,
        betAmountUSD: parseFloat(eventData.betAmount) / 1e6, // Convert from 6 decimals
        chainId: (await this.provider.getNetwork()).chainId,
        network: process.env.NODE_ENV === 'production' ? 'mainnet' : 'testnet',
        placedAt: new Date(eventData.timestamp * 1000),
        isResolved: false
      });

      await spin.save();
      logger.info(`Saved bet to database: RequestId ${eventData.requestId}`);
    } catch (error) {
      logger.error('Error saving bet to database:', error);
    }
  }

  async handleSpinResult(eventData) {
    try {
      const spin = await Spin.findOne({ requestId: eventData.requestId });
      if (!spin) {
        logger.error(`Spin not found for RequestId ${eventData.requestId}`);
        return;
      }

      await spin.resolve(
        eventData.result,
        eventData.multiplier,
        eventData.payout,
        null // randomWord not available in event
      );

      logger.info(`Resolved spin in database: RequestId ${eventData.requestId}`);
    } catch (error) {
      logger.error('Error resolving spin in database:', error);
    }
  }

  async getSpinByRequestId(requestId) {
    try {
      const spinData = await this.spinToWinContract.getSpin(requestId);
      return {
        player: spinData.player,
        betAmount: spinData.betAmount.toString(),
        requestId: spinData.requestId.toString(),
        fulfilled: spinData.fulfilled,
        result: spinData.result.toNumber(),
        payout: spinData.payout.toString(),
        timestamp: spinData.timestamp.toNumber()
      };
    } catch (error) {
      logger.error('Error getting spin from contract:', error);
      throw error;
    }
  }

  async getPlayerBalance(address) {
    try {
      const balance = await this.usdtContract.balanceOf(address);
      return balance.toString();
    } catch (error) {
      logger.error('Error getting player balance:', error);
      throw error;
    }
  }

  async getPlayerAllowance(address) {
    try {
      const allowance = await this.usdtContract.allowance(address, this.spinToWinContract.address);
      return allowance.toString();
    } catch (error) {
      logger.error('Error getting player allowance:', error);
      throw error;
    }
  }

  async getContractStats() {
    try {
      const [totalSpins, totalVolume, totalPayouts, contractBalance, minBet, maxBet, wheelConfig] = await Promise.all([
        this.spinToWinContract.totalSpins(),
        this.spinToWinContract.totalVolume(),
        this.spinToWinContract.totalPayouts(),
        this.spinToWinContract.getContractBalance(),
        this.spinToWinContract.MINIMUM_BET(),
        this.spinToWinContract.getMaxBet(),
        this.spinToWinContract.getWheelConfig()
      ]);

      return {
        totalSpins: totalSpins.toNumber(),
        totalVolume: totalVolume.toString(),
        totalPayouts: totalPayouts.toString(),
        contractBalance: contractBalance.toString(),
        minBet: minBet.toString(),
        maxBet: maxBet.toString(),
        wheelMultipliers: wheelConfig[0].map(m => m.toNumber()),
        wheelOdds: wheelConfig[1].map(o => o.toNumber())
      };
    } catch (error) {
      logger.error('Error getting contract stats:', error);
      throw error;
    }
  }

  async validateBetAmount(amount) {
    try {
      const minBet = await this.spinToWinContract.MINIMUM_BET();
      const maxBet = await this.spinToWinContract.getMaxBet();
      
      const amountBN = ethers.BigNumber.from(amount);
      
      if (amountBN.lt(minBet)) {
        return { valid: false, error: 'Bet amount below minimum' };
      }
      
      if (amountBN.gt(maxBet)) {
        return { valid: false, error: 'Bet amount above maximum' };
      }
      
      return { valid: true };
    } catch (error) {
      logger.error('Error validating bet amount:', error);
      return { valid: false, error: 'Validation failed' };
    }
  }
}

module.exports = new ContractService();
