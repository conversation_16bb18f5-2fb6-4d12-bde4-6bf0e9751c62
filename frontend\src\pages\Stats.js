import React, { useState, useEffect } from 'react';
import { useGame } from '../contexts/GameContext';
import { API_BASE_URL } from '../config/contracts';

const Stats = () => {
  const { gameStats } = useGame();
  const [bigWins, setBigWins] = useState([]);
  const [leaderboard, setLeaderboard] = useState([]);
  const [wheelDistribution, setWheelDistribution] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [timeframe, setTimeframe] = useState('24');

  useEffect(() => {
    loadStatsData();
  }, [timeframe]);

  const loadStatsData = async () => {
    setIsLoading(true);
    try {
      const [bigWinsRes, leaderboardRes, distributionRes] = await Promise.all([
        fetch(`${API_BASE_URL}/api/stats/big-wins?hours=${timeframe}&limit=10`),
        fetch(`${API_BASE_URL}/api/stats/leaderboard?period=${timeframe === '24' ? 'week' : 'month'}&limit=10`),
        fetch(`${API_BASE_URL}/api/stats/wheel-distribution?hours=${timeframe}`)
      ]);

      const [bigWinsData, leaderboardData, distributionData] = await Promise.all([
        bigWinsRes.json(),
        leaderboardRes.json(),
        distributionRes.json()
      ]);

      if (bigWinsData.success) setBigWins(bigWinsData.data);
      if (leaderboardData.success) setLeaderboard(leaderboardData.data);
      if (distributionData.success) setWheelDistribution(distributionData.data);
    } catch (error) {
      console.error('Error loading stats:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const formatNumber = (num) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num?.toFixed(0) || '0';
  };

  return (
    <div className="container" style={{ padding: '2rem 1rem' }}>
      <div className="text-center mb-8">
        <h1>📊 Game Statistics</h1>
        <p className="text-lg opacity-60">
          Comprehensive analytics and leaderboards
        </p>
      </div>

      {/* Timeframe Selector */}
      <div className="card mb-8">
        <div style={{ display: 'flex', gap: '1rem', flexWrap: 'wrap' }}>
          <span style={{ opacity: 0.7, alignSelf: 'center' }}>Timeframe:</span>
          {[
            { key: '24', label: '24 Hours' },
            { key: '168', label: '7 Days' },
            { key: '720', label: '30 Days' }
          ].map(({ key, label }) => (
            <button
              key={key}
              onClick={() => setTimeframe(key)}
              className={`btn ${timeframe === key ? 'btn-primary' : 'btn-secondary'}`}
            >
              {label}
            </button>
          ))}
        </div>
      </div>

      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(400px, 1fr))',
        gap: '2rem'
      }}>
        {/* Global Stats */}
        <div className="card">
          <div className="card-header">
            <h3 className="card-title">🌍 Global Statistics</h3>
          </div>
          
          {gameStats ? (
            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '1rem' }}>
              <div>
                <div className="text-sm opacity-60">Total Spins</div>
                <div className="text-xl font-bold">{formatNumber(gameStats.totalSpins)}</div>
              </div>
              <div>
                <div className="text-sm opacity-60">Total Volume</div>
                <div className="text-xl font-bold">${formatNumber(gameStats.totalVolumeUSD)}</div>
              </div>
              <div>
                <div className="text-sm opacity-60">Total Payouts</div>
                <div className="text-xl font-bold">${formatNumber(gameStats.totalPayoutsUSD)}</div>
              </div>
              <div>
                <div className="text-sm opacity-60">Unique Players</div>
                <div className="text-xl font-bold">{formatNumber(gameStats.uniquePlayers)}</div>
              </div>
              <div>
                <div className="text-sm opacity-60">Win Rate</div>
                <div className="text-xl font-bold">{gameStats.winRate?.toFixed(1)}%</div>
              </div>
              <div>
                <div className="text-sm opacity-60">House Edge</div>
                <div className="text-xl font-bold">{gameStats.houseEdge?.toFixed(1)}%</div>
              </div>
            </div>
          ) : (
            <div className="skeleton" style={{ height: '120px', borderRadius: '0.5rem' }} />
          )}
        </div>

        {/* Big Wins */}
        <div className="card">
          <div className="card-header">
            <h3 className="card-title">🏆 Biggest Wins ({timeframe}h)</h3>
          </div>
          
          {isLoading ? (
            <div style={{ display: 'flex', flexDirection: 'column', gap: '0.75rem' }}>
              {[...Array(5)].map((_, i) => (
                <div key={i} className="skeleton" style={{ height: '60px', borderRadius: '0.5rem' }} />
              ))}
            </div>
          ) : bigWins.length > 0 ? (
            <div style={{ display: 'flex', flexDirection: 'column', gap: '0.75rem' }}>
              {bigWins.map((win, index) => (
                <div
                  key={index}
                  style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    padding: '0.75rem',
                    background: 'rgba(16, 185, 129, 0.1)',
                    borderRadius: '0.5rem',
                    border: '1px solid rgba(16, 185, 129, 0.3)'
                  }}
                >
                  <div>
                    <div style={{ fontWeight: '500' }}>
                      {win.playerAddress}
                    </div>
                    <div style={{ fontSize: '0.875rem', opacity: 0.7 }}>
                      {win.multiplier}× multiplier
                    </div>
                  </div>
                  <div style={{ textAlign: 'right' }}>
                    <div style={{ fontWeight: '500', color: '#10b981' }}>
                      +${win.profit.toFixed(2)}
                    </div>
                    <div style={{ fontSize: '0.875rem', opacity: 0.7 }}>
                      ${win.payoutUSD.toFixed(2)} payout
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div style={{ textAlign: 'center', padding: '2rem', opacity: 0.6 }}>
              No big wins in this timeframe
            </div>
          )}
        </div>

        {/* Leaderboard */}
        <div className="card">
          <div className="card-header">
            <h3 className="card-title">🥇 Leaderboard</h3>
            <p className="card-subtitle">Top players by profit</p>
          </div>
          
          {isLoading ? (
            <div style={{ display: 'flex', flexDirection: 'column', gap: '0.75rem' }}>
              {[...Array(5)].map((_, i) => (
                <div key={i} className="skeleton" style={{ height: '60px', borderRadius: '0.5rem' }} />
              ))}
            </div>
          ) : leaderboard.length > 0 ? (
            <div style={{ display: 'flex', flexDirection: 'column', gap: '0.75rem' }}>
              {leaderboard.map((player, index) => (
                <div
                  key={index}
                  style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    padding: '0.75rem',
                    background: 'rgba(255, 255, 255, 0.05)',
                    borderRadius: '0.5rem'
                  }}
                >
                  <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>
                    <div style={{
                      width: '24px',
                      height: '24px',
                      borderRadius: '50%',
                      background: index === 0 ? '#ffd700' : index === 1 ? '#c0c0c0' : index === 2 ? '#cd7f32' : '#667eea',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      fontSize: '0.75rem',
                      fontWeight: 'bold',
                      color: 'black'
                    }}>
                      {player.rank}
                    </div>
                    <div>
                      <div style={{ fontWeight: '500' }}>
                        {player.playerAddress}
                      </div>
                      <div style={{ fontSize: '0.875rem', opacity: 0.7 }}>
                        {player.totalSpins} spins • {player.winRate.toFixed(1)}% win rate
                      </div>
                    </div>
                  </div>
                  <div style={{ textAlign: 'right' }}>
                    <div style={{
                      fontWeight: '500',
                      color: player.profit >= 0 ? '#10b981' : '#ef4444'
                    }}>
                      {player.profit >= 0 ? '+' : ''}${player.profit.toFixed(2)}
                    </div>
                    <div style={{ fontSize: '0.875rem', opacity: 0.7 }}>
                      ${player.totalBet.toFixed(2)} volume
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div style={{ textAlign: 'center', padding: '2rem', opacity: 0.6 }}>
              No players in this timeframe
            </div>
          )}
        </div>

        {/* Wheel Distribution */}
        <div className="card">
          <div className="card-header">
            <h3 className="card-title">🎯 Wheel Distribution ({timeframe}h)</h3>
            <p className="card-subtitle">
              {wheelDistribution ? `${wheelDistribution.totalSpins} total spins` : 'Loading...'}
            </p>
          </div>
          
          {isLoading || !wheelDistribution ? (
            <div className="skeleton" style={{ height: '200px', borderRadius: '0.5rem' }} />
          ) : (
            <div style={{ display: 'flex', flexDirection: 'column', gap: '0.75rem' }}>
              {wheelDistribution.segments.map((segment, index) => {
                const percentage = wheelDistribution.totalSpins > 0 
                  ? (segment.actualCount / wheelDistribution.totalSpins) * 100 
                  : 0;
                
                return (
                  <div key={index} style={{ display: 'flex', flexDirection: 'column', gap: '0.25rem' }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <span style={{ fontSize: '0.875rem' }}>
                        {segment.multiplier === 0 ? 'Lose' : `${segment.multiplier}× Win`}
                      </span>
                      <span style={{ fontSize: '0.875rem', opacity: 0.7 }}>
                        {segment.actualCount} ({percentage.toFixed(1)}%)
                      </span>
                    </div>
                    <div style={{
                      width: '100%',
                      height: '8px',
                      background: 'rgba(255, 255, 255, 0.1)',
                      borderRadius: '4px',
                      overflow: 'hidden'
                    }}>
                      <div style={{
                        width: `${percentage}%`,
                        height: '100%',
                        background: segment.multiplier === 0 ? '#ef4444' : '#10b981',
                        transition: 'width 0.3s ease'
                      }} />
                    </div>
                    <div style={{ fontSize: '0.75rem', opacity: 0.6 }}>
                      Expected: {segment.expectedOdds}% • Actual: {percentage.toFixed(1)}%
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Stats;
