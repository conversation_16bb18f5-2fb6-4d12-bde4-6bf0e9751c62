version: '3.8'

services:
  # MongoDB Database for Development
  mongodb-dev:
    image: mongo:7
    container_name: spin-to-win-mongodb-dev
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password
      MONGO_INITDB_DATABASE: spin-to-win-dev
    ports:
      - "27017:27017"
    volumes:
      - mongodb_dev_data:/data/db
      - mongodb_dev_config:/data/configdb
    networks:
      - spin-to-win-dev-network

  # Redis Cache for Development
  redis-dev:
    image: redis:7-alpine
    container_name: spin-to-win-redis-dev
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_dev_data:/data
    networks:
      - spin-to-win-dev-network
    command: redis-server --appendonly yes

  # MongoDB Express (Database Admin UI)
  mongo-express:
    image: mongo-express:latest
    container_name: spin-to-win-mongo-express
    restart: unless-stopped
    ports:
      - "8081:8081"
    environment:
      ME_CONFIG_MONGODB_ADMINUSERNAME: admin
      ME_CONFIG_MONGODB_ADMINPASSWORD: password
      ME_CONFIG_MONGODB_URL: ******************************************/
      ME_CONFIG_BASICAUTH_USERNAME: admin
      ME_CONFIG_BASICAUTH_PASSWORD: admin
    depends_on:
      - mongodb-dev
    networks:
      - spin-to-win-dev-network

  # Redis Commander (Redis Admin UI)
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: spin-to-win-redis-commander
    restart: unless-stopped
    ports:
      - "8082:8081"
    environment:
      REDIS_HOSTS: local:redis-dev:6379
    depends_on:
      - redis-dev
    networks:
      - spin-to-win-dev-network

  # Hardhat Node (Local Blockchain)
  hardhat-node:
    build:
      context: ./contracts
      dockerfile: Dockerfile.hardhat
    container_name: spin-to-win-hardhat-node
    restart: unless-stopped
    ports:
      - "8545:8545"
    networks:
      - spin-to-win-dev-network
    command: npx hardhat node --hostname 0.0.0.0

volumes:
  mongodb_dev_data:
    driver: local
  mongodb_dev_config:
    driver: local
  redis_dev_data:
    driver: local

networks:
  spin-to-win-dev-network:
    driver: bridge
