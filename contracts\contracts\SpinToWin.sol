// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "@openzeppelin/contracts/security/Pausable.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";
import "@chainlink/contracts/src/v0.8/interfaces/VRFCoordinatorV2Interface.sol";
import "@chainlink/contracts/src/v0.8/VRFConsumerBaseV2.sol";

/**
 * @title SpinToWin
 * @dev A provably fair spin-to-win game using Chainlink VRF for randomness
 */
contract SpinToWin is VRFConsumerBaseV2, Ownable, ReentrancyGuard, Pausable {
    using SafeERC20 for IERC20;

    // Chainlink VRF variables
    VRFCoordinatorV2Interface private immutable vrfCoordinator;
    uint64 private immutable subscriptionId;
    bytes32 private immutable keyHash;
    uint32 private constant CALLBACK_GAS_LIMIT = 100000;
    uint16 private constant REQUEST_CONFIRMATIONS = 3;
    uint32 private constant NUM_WORDS = 1;

    // Game configuration
    IERC20 public immutable usdtToken;
    uint256 public constant MINIMUM_BET = 2 * 10**6; // 2 USDT (6 decimals)
    uint256 public constant HOUSE_EDGE = 500; // 5% (basis points)
    uint256 public constant BASIS_POINTS = 10000;

    // Wheel segments: [Lose, 1x, 2x, 5x, 10x]
    uint256[] public wheelMultipliers = [0, 1, 2, 5, 10];
    uint256[] public wheelOdds = [4000, 3000, 2000, 800, 200]; // Total: 10000 (100%)

    // Game state
    struct Spin {
        address player;
        uint256 betAmount;
        uint256 requestId;
        bool fulfilled;
        uint256 result;
        uint256 payout;
        uint256 timestamp;
    }

    mapping(uint256 => Spin) public spins;
    mapping(address => uint256[]) public playerSpins;
    uint256 public totalSpins;
    uint256 public totalVolume;
    uint256 public totalPayouts;

    // Events
    event BetPlaced(
        address indexed player,
        uint256 indexed requestId,
        uint256 betAmount,
        uint256 timestamp
    );

    event SpinResult(
        address indexed player,
        uint256 indexed requestId,
        uint256 betAmount,
        uint256 result,
        uint256 multiplier,
        uint256 payout,
        uint256 timestamp
    );

    event WheelConfigUpdated(uint256[] multipliers, uint256[] odds);
    event TreasuryWithdrawn(address indexed to, uint256 amount);

    constructor(
        address _vrfCoordinator,
        uint64 _subscriptionId,
        bytes32 _keyHash,
        address _usdtToken
    ) VRFConsumerBaseV2(_vrfCoordinator) {
        vrfCoordinator = VRFCoordinatorV2Interface(_vrfCoordinator);
        subscriptionId = _subscriptionId;
        keyHash = _keyHash;
        usdtToken = IERC20(_usdtToken);
    }

    /**
     * @dev Place a bet and request randomness from Chainlink VRF
     * @param amount The bet amount in USDT (with 6 decimals)
     */
    function placeBet(uint256 amount) external nonReentrant whenNotPaused {
        require(amount >= MINIMUM_BET, "Bet amount too low");
        require(amount <= getMaxBet(), "Bet amount too high");

        // Transfer USDT from player
        usdtToken.safeTransferFrom(msg.sender, address(this), amount);

        // Request randomness from Chainlink VRF
        uint256 requestId = vrfCoordinator.requestRandomWords(
            keyHash,
            subscriptionId,
            REQUEST_CONFIRMATIONS,
            CALLBACK_GAS_LIMIT,
            NUM_WORDS
        );

        // Store spin data
        spins[requestId] = Spin({
            player: msg.sender,
            betAmount: amount,
            requestId: requestId,
            fulfilled: false,
            result: 0,
            payout: 0,
            timestamp: block.timestamp
        });

        playerSpins[msg.sender].push(requestId);
        totalSpins++;
        totalVolume += amount;

        emit BetPlaced(msg.sender, requestId, amount, block.timestamp);
    }

    /**
     * @dev Callback function used by VRF Coordinator
     */
    function fulfillRandomWords(
        uint256 requestId,
        uint256[] memory randomWords
    ) internal override {
        Spin storage spin = spins[requestId];
        require(spin.player != address(0), "Invalid request ID");
        require(!spin.fulfilled, "Already fulfilled");

        uint256 randomNumber = randomWords[0] % BASIS_POINTS;
        uint256 segmentIndex = getWheelSegment(randomNumber);
        uint256 multiplier = wheelMultipliers[segmentIndex];
        uint256 payout = (spin.betAmount * multiplier);

        spin.fulfilled = true;
        spin.result = segmentIndex;
        spin.payout = payout;

        if (payout > 0) {
            usdtToken.safeTransfer(spin.player, payout);
            totalPayouts += payout;
        }

        emit SpinResult(
            spin.player,
            requestId,
            spin.betAmount,
            segmentIndex,
            multiplier,
            payout,
            block.timestamp
        );
    }

    /**
     * @dev Determine which wheel segment the random number lands on
     */
    function getWheelSegment(uint256 randomNumber) internal view returns (uint256) {
        uint256 cumulativeOdds = 0;
        for (uint256 i = 0; i < wheelOdds.length; i++) {
            cumulativeOdds += wheelOdds[i];
            if (randomNumber < cumulativeOdds) {
                return i;
            }
        }
        return wheelOdds.length - 1; // Fallback to last segment
    }

    /**
     * @dev Calculate maximum bet based on contract balance and house edge
     */
    function getMaxBet() public view returns (uint256) {
        uint256 balance = usdtToken.balanceOf(address(this));
        uint256 maxPayout = (balance * HOUSE_EDGE) / BASIS_POINTS;
        return maxPayout / 10; // Max 10x multiplier
    }

    /**
     * @dev Get player's spin history
     */
    function getPlayerSpins(address player) external view returns (uint256[] memory) {
        return playerSpins[player];
    }

    /**
     * @dev Get spin details by request ID
     */
    function getSpin(uint256 requestId) external view returns (Spin memory) {
        return spins[requestId];
    }

    // Owner functions
    function setWheelSegments(
        uint256[] calldata multipliers,
        uint256[] calldata odds
    ) external onlyOwner {
        require(multipliers.length == odds.length, "Array length mismatch");
        require(multipliers.length > 0, "Empty arrays");
        
        uint256 totalOdds = 0;
        for (uint256 i = 0; i < odds.length; i++) {
            totalOdds += odds[i];
        }
        require(totalOdds == BASIS_POINTS, "Odds must sum to 10000");

        wheelMultipliers = multipliers;
        wheelOdds = odds;

        emit WheelConfigUpdated(multipliers, odds);
    }

    function withdrawTreasury(address to, uint256 amount) external onlyOwner {
        require(to != address(0), "Invalid address");
        require(amount > 0, "Invalid amount");
        
        uint256 balance = usdtToken.balanceOf(address(this));
        require(amount <= balance, "Insufficient balance");

        usdtToken.safeTransfer(to, amount);
        emit TreasuryWithdrawn(to, amount);
    }

    function pause() external onlyOwner {
        _pause();
    }

    function unpause() external onlyOwner {
        _unpause();
    }

    // View functions
    function getContractBalance() external view returns (uint256) {
        return usdtToken.balanceOf(address(this));
    }

    function getWheelConfig() external view returns (uint256[] memory, uint256[] memory) {
        return (wheelMultipliers, wheelOdds);
    }
}
