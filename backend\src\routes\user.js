const express = require('express');
const Joi = require('joi');
const Spin = require('../models/Spin');
const contractService = require('../services/contractService');
const logger = require('../utils/logger');

const router = express.Router();

// Validation schemas
const addressSchema = Joi.object({
  address: Joi.string().pattern(/^0x[a-fA-F0-9]{40}$/).required()
});

const historyQuerySchema = Joi.object({
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(20),
  resolved: Joi.boolean().optional()
});

/**
 * GET /api/user/history/:address
 * Get user's spin history with pagination
 */
router.get('/history/:address', async (req, res) => {
  try {
    // Validate address
    const { error: addressError, value: addressValue } = addressSchema.validate(req.params);
    if (addressError) {
      return res.status(400).json({
        error: 'Invalid address format',
        details: addressError.details[0].message
      });
    }

    // Validate query parameters
    const { error: queryError, value: queryValue } = historyQuerySchema.validate(req.query);
    if (queryError) {
      return res.status(400).json({
        error: 'Invalid query parameters',
        details: queryError.details[0].message
      });
    }

    const { address } = addressValue;
    const { page, limit, resolved } = queryValue;
    const playerAddress = address.toLowerCase();

    // Build query
    const query = { playerAddress };
    if (resolved !== undefined) {
      query.isResolved = resolved;
    }

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Get spins with pagination
    const [spins, totalCount] = await Promise.all([
      Spin.find(query)
        .sort({ placedAt: -1 })
        .skip(skip)
        .limit(limit)
        .lean(),
      Spin.countDocuments(query)
    ]);

    // Calculate pagination info
    const totalPages = Math.ceil(totalCount / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    res.json({
      success: true,
      data: {
        spins: spins.map(spin => ({
          requestId: spin.requestId,
          betAmount: spin.betAmount,
          betAmountUSD: spin.betAmountUSD,
          isResolved: spin.isResolved,
          segmentIndex: spin.segmentIndex,
          multiplier: spin.multiplier,
          payout: spin.payout,
          payoutUSD: spin.payoutUSD,
          profitLoss: parseFloat(spin.payout || '0') - parseFloat(spin.betAmount),
          isWin: spin.multiplier > 0,
          placedAt: spin.placedAt,
          resolvedAt: spin.resolvedAt,
          transactionHash: spin.transactionHash
        })),
        pagination: {
          currentPage: page,
          totalPages,
          totalCount,
          hasNextPage,
          hasPrevPage,
          limit
        }
      }
    });

  } catch (error) {
    logger.error('Error getting user history:', error);
    res.status(500).json({
      error: 'Internal server error',
      details: 'Failed to get user history'
    });
  }
});

/**
 * GET /api/user/stats/:address
 * Get user's statistics
 */
router.get('/stats/:address', async (req, res) => {
  try {
    // Validate address
    const { error, value } = addressSchema.validate(req.params);
    if (error) {
      return res.status(400).json({
        error: 'Invalid address format',
        details: error.details[0].message
      });
    }

    const { address } = value;
    const playerAddress = address.toLowerCase();

    // Get player stats from database
    const stats = await Spin.getPlayerStats(playerAddress);

    // Get current balance and allowance
    const [balance, allowance] = await Promise.all([
      contractService.getPlayerBalance(address),
      contractService.getPlayerAllowance(address)
    ]);

    res.json({
      success: true,
      data: {
        ...stats,
        currentBalance: balance,
        currentBalanceUSD: parseFloat(balance) / 1e6,
        allowance: allowance,
        allowanceUSD: parseFloat(allowance) / 1e6
      }
    });

  } catch (error) {
    logger.error('Error getting user stats:', error);
    res.status(500).json({
      error: 'Internal server error',
      details: 'Failed to get user stats'
    });
  }
});

/**
 * GET /api/user/balance/:address
 * Get user's current USDT balance and allowance
 */
router.get('/balance/:address', async (req, res) => {
  try {
    // Validate address
    const { error, value } = addressSchema.validate(req.params);
    if (error) {
      return res.status(400).json({
        error: 'Invalid address format',
        details: error.details[0].message
      });
    }

    const { address } = value;

    // Get balance and allowance
    const [balance, allowance] = await Promise.all([
      contractService.getPlayerBalance(address),
      contractService.getPlayerAllowance(address)
    ]);

    res.json({
      success: true,
      data: {
        balance,
        balanceUSD: parseFloat(balance) / 1e6,
        allowance,
        allowanceUSD: parseFloat(allowance) / 1e6,
        contractAddress: process.env.SPIN_TO_WIN_CONTRACT_ADDRESS,
        usdtAddress: process.env.USDT_CONTRACT_ADDRESS
      }
    });

  } catch (error) {
    logger.error('Error getting user balance:', error);
    res.status(500).json({
      error: 'Internal server error',
      details: 'Failed to get user balance'
    });
  }
});

/**
 * GET /api/user/recent-wins/:address
 * Get user's recent wins
 */
router.get('/recent-wins/:address', async (req, res) => {
  try {
    // Validate address
    const { error, value } = addressSchema.validate(req.params);
    if (error) {
      return res.status(400).json({
        error: 'Invalid address format',
        details: error.details[0].message
      });
    }

    const { address } = value;
    const playerAddress = address.toLowerCase();

    // Get recent wins (last 10)
    const recentWins = await Spin.find({
      playerAddress,
      isResolved: true,
      multiplier: { $gt: 0 }
    })
    .sort({ resolvedAt: -1 })
    .limit(10)
    .lean();

    res.json({
      success: true,
      data: recentWins.map(spin => ({
        requestId: spin.requestId,
        betAmount: spin.betAmount,
        betAmountUSD: spin.betAmountUSD,
        multiplier: spin.multiplier,
        payout: spin.payout,
        payoutUSD: spin.payoutUSD,
        profit: parseFloat(spin.payout) - parseFloat(spin.betAmount),
        resolvedAt: spin.resolvedAt,
        transactionHash: spin.transactionHash
      }))
    });

  } catch (error) {
    logger.error('Error getting recent wins:', error);
    res.status(500).json({
      error: 'Internal server error',
      details: 'Failed to get recent wins'
    });
  }
});

module.exports = router;
