import React, { useState, useEffect } from 'react';
import toast from 'react-hot-toast';

import { useWeb3 } from '../contexts/Web3Context';
import { useGame } from '../contexts/GameContext';
import Wheel from '../components/Wheel';
import GameStats from '../components/GameStats';
import RecentSpins from '../components/RecentSpins';
import { GAME_CONFIG, WHEEL_SEGMENTS } from '../config/contracts';

const Home = () => {
  const {
    account,
    isConnected,
    usdtBalance,
    usdtAllowance,
    approveUSDT,
    placeBet: placeBetContract,
    isSupported
  } = useWeb3();

  const {
    gameStats,
    userStats,
    pendingSpins,
    placeBet: placeBetAPI,
    getSpinResult,
    refreshData
  } = useGame();

  const [betAmount, setBetAmount] = useState('2');
  const [isSpinning, setIsSpinning] = useState(false);
  const [spinResult, setSpinResult] = useState(null);
  const [isApproving, setIsApproving] = useState(false);
  const [lastTxHash, setLastTxHash] = useState(null);

  // Check for pending spins and poll for results
  useEffect(() => {
    if (pendingSpins.length > 0 && !isSpinning) {
      checkPendingSpins();
    }
  }, [pendingSpins]);

  const checkPendingSpins = async () => {
    for (const spin of pendingSpins) {
      const result = await getSpinResult(spin.transactionHash);
      if (result && result.isResolved) {
        // Found a resolved spin, trigger animation
        setIsSpinning(true);
        setSpinResult(result.segmentIndex);
        setLastTxHash(spin.transactionHash);
        break;
      }
    }
  };

  const handleBetAmountChange = (e) => {
    const value = e.target.value;
    if (value === '' || /^\d*\.?\d*$/.test(value)) {
      setBetAmount(value);
    }
  };

  const needsApproval = () => {
    if (!betAmount || !usdtAllowance) return false;
    return parseFloat(usdtAllowance) < parseFloat(betAmount);
  };

  const handleApprove = async () => {
    try {
      setIsApproving(true);
      await approveUSDT(betAmount);
    } catch (error) {
      console.error('Approval failed:', error);
    } finally {
      setIsApproving(false);
    }
  };

  const handleSpin = async () => {
    try {
      if (!isConnected) {
        toast.error('Please connect your wallet');
        return;
      }

      if (!isSupported) {
        toast.error('Please switch to a supported network');
        return;
      }

      if (!betAmount || parseFloat(betAmount) < GAME_CONFIG.minBet) {
        toast.error(`Minimum bet is ${GAME_CONFIG.minBet} USDT`);
        return;
      }

      if (parseFloat(usdtBalance) < parseFloat(betAmount)) {
        toast.error('Insufficient USDT balance');
        return;
      }

      if (needsApproval()) {
        toast.error('Please approve USDT spending first');
        return;
      }

      // Validate with API first
      const apiValidation = await placeBetAPI(betAmount);
      if (!apiValidation) {
        return;
      }

      // Place bet on contract
      const result = await placeBetContract(betAmount);
      if (result) {
        setLastTxHash(result.tx.hash);
        toast.success('Bet placed! Waiting for result...');

        // Start checking for result
        setTimeout(() => {
          checkForResult(result.tx.hash);
        }, 5000); // Wait 5 seconds before checking
      }
    } catch (error) {
      console.error('Spin failed:', error);
      toast.error('Failed to place bet');
    }
  };

  const checkForResult = async (txHash) => {
    let attempts = 0;
    const maxAttempts = 30; // Check for 5 minutes

    const checkInterval = setInterval(async () => {
      attempts++;

      const result = await getSpinResult(txHash);
      if (result && result.isResolved) {
        clearInterval(checkInterval);
        setIsSpinning(true);
        setSpinResult(result.segmentIndex);
      } else if (attempts >= maxAttempts) {
        clearInterval(checkInterval);
        toast.error('Result taking longer than expected. Check your history.');
      }
    }, 10000); // Check every 10 seconds
  };

  const handleSpinComplete = () => {
    setIsSpinning(false);
    setSpinResult(null);
    refreshData();

    if (lastTxHash) {
      // Show final result
      getSpinResult(lastTxHash).then(result => {
        if (result) {
          const segment = WHEEL_SEGMENTS[result.segmentIndex];
          if (segment.multiplier > 0) {
            toast.success(
              `🎉 You won ${segment.multiplier}× your bet! (+${result.payoutUSD.toFixed(2)} USDT)`,
              { duration: 6000 }
            );
          } else {
            toast.error('Better luck next time!', { duration: 4000 });
          }
        }
      });
      setLastTxHash(null);
    }
  };

  const canSpin = isConnected &&
                 isSupported &&
                 !isSpinning &&
                 betAmount &&
                 parseFloat(betAmount) >= GAME_CONFIG.minBet &&
                 parseFloat(usdtBalance) >= parseFloat(betAmount) &&
                 !needsApproval() &&
                 pendingSpins.length === 0;

  return (
    <div className="container" style={{ padding: '2rem 1rem' }}>
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'minmax(0, 1fr) 400px',
        gap: '3rem',
        alignItems: 'start'
      }} className="responsive-grid">
        {/* Main Game Area */}
        <div className="fade-in">
          <div className="text-center mb-8">
            <h1>🎰 Spin to Win</h1>
            <p className="text-lg opacity-60">
              Place your bet and spin the wheel for a chance to win up to 10× your bet!
            </p>
          </div>

          {/* Wheel */}
          <div className="text-center mb-8">
            <Wheel
              isSpinning={isSpinning}
              result={spinResult}
              onSpinComplete={handleSpinComplete}
            />
          </div>

          {/* Bet Controls */}
          <div className="card" style={{ maxWidth: '500px', margin: '0 auto' }}>
            <div className="card-header">
              <h3 className="card-title">Place Your Bet</h3>
              <p className="card-subtitle">
                Minimum bet: {GAME_CONFIG.minBet} USDT
              </p>
            </div>

            <div className="form-group">
              <label className="form-label">Bet Amount (USDT)</label>
              <input
                type="text"
                value={betAmount}
                onChange={handleBetAmountChange}
                placeholder="Enter amount"
                className="form-input"
                disabled={isSpinning}
              />
              <div style={{
                display: 'flex',
                gap: '0.5rem',
                marginTop: '0.5rem',
                flexWrap: 'wrap'
              }}>
                {[2, 5, 10, 25, 50].map(amount => (
                  <button
                    key={amount}
                    onClick={() => setBetAmount(amount.toString())}
                    className="btn btn-sm btn-secondary"
                    disabled={isSpinning}
                  >
                    {amount}
                  </button>
                ))}
              </div>
            </div>

            {/* Balance Info */}
            {isConnected && (
              <div style={{
                background: 'rgba(255, 255, 255, 0.05)',
                padding: '1rem',
                borderRadius: '0.5rem',
                marginBottom: '1.5rem'
              }}>
                <div className="flex justify-between text-sm">
                  <span>USDT Balance:</span>
                  <span>{parseFloat(usdtBalance).toFixed(2)} USDT</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Allowance:</span>
                  <span>{parseFloat(usdtAllowance).toFixed(2)} USDT</span>
                </div>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex flex-col gap-4">
              {!isConnected ? (
                <div className="text-center">
                  <p className="mb-4">Connect your wallet to start playing</p>
                </div>
              ) : !isSupported ? (
                <div className="text-center">
                  <p className="mb-4 text-danger">
                    Please switch to a supported network
                  </p>
                </div>
              ) : needsApproval() ? (
                <button
                  onClick={handleApprove}
                  disabled={isApproving}
                  className="btn btn-primary btn-lg"
                >
                  {isApproving ? (
                    <>
                      <div className="loading-spinner" />
                      Approving...
                    </>
                  ) : (
                    `Approve ${betAmount} USDT`
                  )}
                </button>
              ) : (
                <button
                  onClick={handleSpin}
                  disabled={!canSpin}
                  className="btn btn-success btn-lg"
                >
                  {isSpinning ? (
                    <>
                      <div className="loading-spinner" />
                      Spinning...
                    </>
                  ) : pendingSpins.length > 0 ? (
                    'Waiting for previous spin...'
                  ) : (
                    `Spin for ${betAmount} USDT`
                  )}
                </button>
              )}
            </div>

            {/* Pending Spins Warning */}
            {pendingSpins.length > 0 && (
              <div style={{
                background: 'rgba(251, 191, 36, 0.1)',
                border: '1px solid rgba(251, 191, 36, 0.3)',
                padding: '1rem',
                borderRadius: '0.5rem',
                marginTop: '1rem'
              }}>
                <p className="text-sm">
                  ⏳ You have {pendingSpins.length} pending spin(s).
                  Please wait for the result before placing another bet.
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Sidebar */}
        <div style={{ display: 'flex', flexDirection: 'column', gap: '2rem' }}>
          <GameStats stats={gameStats} userStats={userStats} />
          <RecentSpins />
        </div>
      </div>
    </div>
  );
};

export default Home;
