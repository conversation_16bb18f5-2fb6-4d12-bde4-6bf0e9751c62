import React, { useState, useRef, useEffect } from 'react';
import { motion } from 'framer-motion';
import { WHEEL_SEGMENTS } from '../config/contracts';

const Wheel = ({ isSpinning, result, onSpinComplete }) => {
  const [rotation, setRotation] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);
  const wheelRef = useRef(null);

  // Calculate segment angle
  const segmentAngle = 360 / WHEEL_SEGMENTS.length;

  useEffect(() => {
    if (isSpinning && result !== null && !isAnimating) {
      spinToResult(result);
    }
  }, [isSpinning, result, isAnimating]);

  const spinToResult = (resultIndex) => {
    setIsAnimating(true);
    
    // Calculate target angle for the result
    const targetSegmentAngle = resultIndex * segmentAngle;
    
    // Add multiple full rotations for effect (3-5 full spins)
    const fullRotations = 3 + Math.random() * 2;
    const totalRotation = fullRotations * 360;
    
    // Calculate final angle (we need to account for the pointer position)
    // The pointer is at the top, so we need to adjust for that
    const pointerOffset = 90; // Pointer is at 90 degrees (top)
    const finalAngle = totalRotation + (360 - targetSegmentAngle) - pointerOffset;
    
    setRotation(prev => prev + finalAngle);
    
    // Complete animation after 4 seconds
    setTimeout(() => {
      setIsAnimating(false);
      if (onSpinComplete) {
        onSpinComplete();
      }
    }, 4000);
  };

  const renderSegments = () => {
    return WHEEL_SEGMENTS.map((segment, index) => {
      const startAngle = index * segmentAngle;
      const endAngle = (index + 1) * segmentAngle;
      
      // Calculate path for SVG segment
      const centerX = 150;
      const centerY = 150;
      const radius = 140;
      
      const startAngleRad = (startAngle * Math.PI) / 180;
      const endAngleRad = (endAngle * Math.PI) / 180;
      
      const x1 = centerX + radius * Math.cos(startAngleRad);
      const y1 = centerY + radius * Math.sin(startAngleRad);
      const x2 = centerX + radius * Math.cos(endAngleRad);
      const y2 = centerY + radius * Math.sin(endAngleRad);
      
      const largeArcFlag = segmentAngle > 180 ? 1 : 0;
      
      const pathData = [
        `M ${centerX} ${centerY}`,
        `L ${x1} ${y1}`,
        `A ${radius} ${radius} 0 ${largeArcFlag} 1 ${x2} ${y2}`,
        'Z'
      ].join(' ');
      
      // Calculate text position
      const textAngle = startAngle + segmentAngle / 2;
      const textAngleRad = (textAngle * Math.PI) / 180;
      const textRadius = radius * 0.7;
      const textX = centerX + textRadius * Math.cos(textAngleRad);
      const textY = centerY + textRadius * Math.sin(textAngleRad);
      
      return (
        <g key={index}>
          <path
            d={pathData}
            fill={segment.color}
            stroke="#ffffff"
            strokeWidth="2"
            style={{
              filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.2))'
            }}
          />
          <text
            x={textX}
            y={textY}
            fill="white"
            fontSize="16"
            fontWeight="bold"
            textAnchor="middle"
            dominantBaseline="middle"
            style={{
              textShadow: '1px 1px 2px rgba(0,0,0,0.5)',
              pointerEvents: 'none'
            }}
          >
            {segment.label}
          </text>
          <text
            x={textX}
            y={textY + 20}
            fill="rgba(255,255,255,0.8)"
            fontSize="12"
            textAnchor="middle"
            dominantBaseline="middle"
            style={{
              textShadow: '1px 1px 2px rgba(0,0,0,0.5)',
              pointerEvents: 'none'
            }}
          >
            {segment.odds}%
          </text>
        </g>
      );
    });
  };

  return (
    <div style={{
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      gap: '2rem'
    }}>
      {/* Wheel Container */}
      <div style={{
        position: 'relative',
        width: '300px',
        height: '300px'
      }}>
        {/* Pointer */}
        <div style={{
          position: 'absolute',
          top: '-10px',
          left: '50%',
          transform: 'translateX(-50%)',
          width: 0,
          height: 0,
          borderLeft: '15px solid transparent',
          borderRight: '15px solid transparent',
          borderBottom: '30px solid #ffffff',
          zIndex: 10,
          filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.3))'
        }} />
        
        {/* Wheel */}
        <motion.div
          ref={wheelRef}
          animate={{ rotate: rotation }}
          transition={{
            duration: isAnimating ? 4 : 0,
            ease: isAnimating ? [0.25, 0.46, 0.45, 0.94] : 'linear'
          }}
          style={{
            width: '100%',
            height: '100%',
            borderRadius: '50%',
            overflow: 'hidden',
            boxShadow: '0 8px 32px rgba(0, 0, 0, 0.3)',
            border: '4px solid rgba(255, 255, 255, 0.3)'
          }}
        >
          <svg width="300" height="300" viewBox="0 0 300 300">
            {renderSegments()}
            
            {/* Center circle */}
            <circle
              cx="150"
              cy="150"
              r="20"
              fill="rgba(255, 255, 255, 0.9)"
              stroke="#667eea"
              strokeWidth="3"
            />
            <circle
              cx="150"
              cy="150"
              r="8"
              fill="#667eea"
            />
          </svg>
        </motion.div>
        
        {/* Spinning overlay */}
        {isSpinning && (
          <div style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: 'rgba(0, 0, 0, 0.1)',
            borderRadius: '50%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 5
          }}>
            <div style={{
              color: 'white',
              fontSize: '1.2rem',
              fontWeight: 'bold',
              textShadow: '2px 2px 4px rgba(0,0,0,0.8)',
              animation: 'pulse 1s infinite'
            }}>
              SPINNING...
            </div>
          </div>
        )}
      </div>
      
      {/* Legend */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(120px, 1fr))',
        gap: '1rem',
        width: '100%',
        maxWidth: '600px'
      }}>
        {WHEEL_SEGMENTS.map((segment, index) => (
          <div
            key={index}
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem',
              padding: '0.5rem',
              background: 'rgba(255, 255, 255, 0.1)',
              borderRadius: '0.5rem',
              fontSize: '0.875rem'
            }}
          >
            <div style={{
              width: '16px',
              height: '16px',
              borderRadius: '50%',
              background: segment.color,
              flexShrink: 0
            }} />
            <span>{segment.label}</span>
            <span style={{ opacity: 0.7, marginLeft: 'auto' }}>
              {segment.odds}%
            </span>
          </div>
        ))}
      </div>
      
      <style jsx>{`
        @keyframes pulse {
          0%, 100% { opacity: 1; }
          50% { opacity: 0.7; }
        }
      `}</style>
    </div>
  );
};

export default Wheel;
