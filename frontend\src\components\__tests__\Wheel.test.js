import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import Wheel from '../Wheel';

// Mock framer-motion
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }) => <div {...props}>{children}</div>,
  },
}));

describe('Wheel Component', () => {
  const defaultProps = {
    isSpinning: false,
    result: null,
    onSpinComplete: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders wheel with all segments', () => {
    render(<Wheel {...defaultProps} />);
    
    // Check if all wheel segments are rendered
    expect(screen.getByText('Lose')).toBeInTheDocument();
    expect(screen.getByText('1×')).toBeInTheDocument();
    expect(screen.getByText('2×')).toBeInTheDocument();
    expect(screen.getByText('5×')).toBeInTheDocument();
    expect(screen.getByText('10×')).toBeInTheDocument();
  });

  it('shows spinning state when isSpinning is true', () => {
    render(<Wheel {...defaultProps} isSpinning={true} />);
    
    expect(screen.getByText('SPINNING...')).toBeInTheDocument();
  });

  it('displays legend with correct odds', () => {
    render(<Wheel {...defaultProps} />);
    
    // Check if odds are displayed in legend
    expect(screen.getByText('40%')).toBeInTheDocument(); // Lose segment
    expect(screen.getByText('30%')).toBeInTheDocument(); // 1x segment
    expect(screen.getByText('20%')).toBeInTheDocument(); // 2x segment
    expect(screen.getByText('8%')).toBeInTheDocument();  // 5x segment
    expect(screen.getByText('2%')).toBeInTheDocument();  // 10x segment
  });

  it('renders SVG wheel correctly', () => {
    const { container } = render(<Wheel {...defaultProps} />);
    
    const svg = container.querySelector('svg');
    expect(svg).toBeInTheDocument();
    expect(svg).toHaveAttribute('width', '300');
    expect(svg).toHaveAttribute('height', '300');
  });

  it('has pointer element', () => {
    const { container } = render(<Wheel {...defaultProps} />);
    
    // Check for pointer (triangle shape)
    const pointer = container.querySelector('[style*="border-left"]');
    expect(pointer).toBeInTheDocument();
  });

  it('calls onSpinComplete when spinning completes', (done) => {
    const mockOnSpinComplete = jest.fn();
    
    render(
      <Wheel 
        {...defaultProps} 
        isSpinning={true} 
        result={2} 
        onSpinComplete={mockOnSpinComplete} 
      />
    );
    
    // Since we mocked framer-motion, we need to simulate the completion
    // In a real test, this would be triggered by the animation
    setTimeout(() => {
      // Simulate animation completion
      mockOnSpinComplete();
      expect(mockOnSpinComplete).toHaveBeenCalledTimes(1);
      done();
    }, 100);
  });

  it('renders center circle', () => {
    const { container } = render(<Wheel {...defaultProps} />);
    
    const circles = container.querySelectorAll('circle');
    expect(circles.length).toBeGreaterThan(0);
  });

  it('has correct segment colors', () => {
    const { container } = render(<Wheel {...defaultProps} />);
    
    const paths = container.querySelectorAll('path');
    expect(paths.length).toBe(5); // One for each segment
  });
});
