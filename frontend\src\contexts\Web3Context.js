import React, { createContext, useContext, useEffect, useState } from 'react';
import { ethers } from 'ethers';
import toast from 'react-hot-toast';

import { CONTRACTS, SUPPORTED_CHAINS } from '../config/contracts';

const Web3Context = createContext();

export const useWeb3 = () => {
  const context = useContext(Web3Context);
  if (!context) {
    throw new Error('useWeb3 must be used within a Web3Provider');
  }
  return context;
};

export const Web3Provider = ({ children }) => {
  const [account, setAccount] = useState(null);
  const [chainId, setChainId] = useState(null);
  const [provider, setProvider] = useState(null);
  const [signer, setSigner] = useState(null);
  const [isConnecting, setIsConnecting] = useState(false);
  const [isConnected, setIsConnected] = useState(false);
  const [balance, setBalance] = useState('0');
  const [usdtBalance, setUsdtBalance] = useState('0');
  const [usdtAllowance, setUsdtAllowance] = useState('0');

  // Contract instances
  const [spinToWinContract, setSpinToWinContract] = useState(null);
  const [usdtContract, setUsdtContract] = useState(null);

  // Check if wallet is connected on mount
  useEffect(() => {
    checkConnection();
  }, []);

  // Set up event listeners
  useEffect(() => {
    if (window.ethereum) {
      window.ethereum.on('accountsChanged', handleAccountsChanged);
      window.ethereum.on('chainChanged', handleChainChanged);
      window.ethereum.on('disconnect', handleDisconnect);

      return () => {
        window.ethereum.removeListener('accountsChanged', handleAccountsChanged);
        window.ethereum.removeListener('chainChanged', handleChainChanged);
        window.ethereum.removeListener('disconnect', handleDisconnect);
      };
    }
  }, []);

  // Update balances when account or chain changes
  useEffect(() => {
    if (account && provider && chainId) {
      updateBalances();
    }
  }, [account, provider, chainId]);

  const checkConnection = async () => {
    try {
      if (window.ethereum) {
        const provider = new ethers.providers.Web3Provider(window.ethereum);
        const accounts = await provider.listAccounts();
        
        if (accounts.length > 0) {
          const network = await provider.getNetwork();
          await connectWallet(false);
        }
      }
    } catch (error) {
      console.error('Error checking connection:', error);
    }
  };

  const connectWallet = async (requestPermission = true) => {
    try {
      if (!window.ethereum) {
        toast.error('Please install MetaMask or another Web3 wallet');
        return false;
      }

      setIsConnecting(true);

      const provider = new ethers.providers.Web3Provider(window.ethereum);
      
      let accounts;
      if (requestPermission) {
        accounts = await window.ethereum.request({
          method: 'eth_requestAccounts',
        });
      } else {
        accounts = await provider.listAccounts();
      }

      if (accounts.length === 0) {
        throw new Error('No accounts found');
      }

      const network = await provider.getNetwork();
      const signer = provider.getSigner();

      // Check if chain is supported
      if (!SUPPORTED_CHAINS.includes(network.chainId)) {
        toast.error(`Unsupported network. Please switch to ${SUPPORTED_CHAINS.map(id => CONTRACTS[id]?.name).join(' or ')}`);
        return false;
      }

      // Set up contracts
      const contractConfig = CONTRACTS[network.chainId];
      if (!contractConfig) {
        toast.error('Contracts not deployed on this network');
        return false;
      }

      const spinContract = new ethers.Contract(
        contractConfig.spinToWin,
        contractConfig.spinToWinAbi,
        signer
      );

      const usdtContract = new ethers.Contract(
        contractConfig.usdt,
        contractConfig.usdtAbi,
        signer
      );

      // Update state
      setAccount(accounts[0]);
      setChainId(network.chainId);
      setProvider(provider);
      setSigner(signer);
      setSpinToWinContract(spinContract);
      setUsdtContract(usdtContract);
      setIsConnected(true);

      if (requestPermission) {
        toast.success('Wallet connected successfully!');
      }

      return true;
    } catch (error) {
      console.error('Error connecting wallet:', error);
      if (error.code === 4001) {
        toast.error('Please connect your wallet to continue');
      } else {
        toast.error('Failed to connect wallet');
      }
      return false;
    } finally {
      setIsConnecting(false);
    }
  };

  const disconnectWallet = () => {
    setAccount(null);
    setChainId(null);
    setProvider(null);
    setSigner(null);
    setSpinToWinContract(null);
    setUsdtContract(null);
    setIsConnected(false);
    setBalance('0');
    setUsdtBalance('0');
    setUsdtAllowance('0');
    toast.success('Wallet disconnected');
  };

  const switchNetwork = async (targetChainId) => {
    try {
      await window.ethereum.request({
        method: 'wallet_switchEthereumChain',
        params: [{ chainId: `0x${targetChainId.toString(16)}` }],
      });
      return true;
    } catch (error) {
      console.error('Error switching network:', error);
      toast.error('Failed to switch network');
      return false;
    }
  };

  const updateBalances = async () => {
    try {
      if (!account || !provider || !usdtContract) return;

      const [ethBalance, usdtBal, allowance] = await Promise.all([
        provider.getBalance(account),
        usdtContract.balanceOf(account),
        usdtContract.allowance(account, spinToWinContract?.address || ethers.constants.AddressZero)
      ]);

      setBalance(ethers.utils.formatEther(ethBalance));
      setUsdtBalance(ethers.utils.formatUnits(usdtBal, 6)); // USDT has 6 decimals
      setUsdtAllowance(ethers.utils.formatUnits(allowance, 6));
    } catch (error) {
      console.error('Error updating balances:', error);
    }
  };

  const approveUSDT = async (amount) => {
    try {
      if (!usdtContract || !spinToWinContract) {
        throw new Error('Contracts not initialized');
      }

      const amountWei = ethers.utils.parseUnits(amount.toString(), 6);
      const tx = await usdtContract.approve(spinToWinContract.address, amountWei);
      
      toast.loading('Approving USDT...', { id: 'approve' });
      await tx.wait();
      
      toast.success('USDT approved successfully!', { id: 'approve' });
      await updateBalances();
      
      return tx;
    } catch (error) {
      console.error('Error approving USDT:', error);
      toast.error('Failed to approve USDT', { id: 'approve' });
      throw error;
    }
  };

  const placeBet = async (amount) => {
    try {
      if (!spinToWinContract) {
        throw new Error('Contract not initialized');
      }

      const amountWei = ethers.utils.parseUnits(amount.toString(), 6);
      const tx = await spinToWinContract.placeBet(amountWei);
      
      toast.loading('Placing bet...', { id: 'bet' });
      const receipt = await tx.wait();
      
      toast.success('Bet placed successfully!', { id: 'bet' });
      await updateBalances();
      
      return { tx, receipt };
    } catch (error) {
      console.error('Error placing bet:', error);
      toast.error('Failed to place bet', { id: 'bet' });
      throw error;
    }
  };

  // Event handlers
  const handleAccountsChanged = (accounts) => {
    if (accounts.length === 0) {
      disconnectWallet();
    } else if (accounts[0] !== account) {
      setAccount(accounts[0]);
      updateBalances();
    }
  };

  const handleChainChanged = (chainId) => {
    window.location.reload(); // Reload to reset state
  };

  const handleDisconnect = () => {
    disconnectWallet();
  };

  const value = {
    // State
    account,
    chainId,
    provider,
    signer,
    isConnecting,
    isConnected,
    balance,
    usdtBalance,
    usdtAllowance,
    spinToWinContract,
    usdtContract,

    // Actions
    connectWallet,
    disconnectWallet,
    switchNetwork,
    updateBalances,
    approveUSDT,
    placeBet,

    // Utils
    isSupported: chainId ? SUPPORTED_CHAINS.includes(chainId) : false,
    networkName: chainId ? CONTRACTS[chainId]?.name : 'Unknown',
  };

  return (
    <Web3Context.Provider value={value}>
      {children}
    </Web3Context.Provider>
  );
};
