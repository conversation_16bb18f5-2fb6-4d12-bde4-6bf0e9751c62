import React, { useState, useEffect } from 'react';
import { useWeb3 } from '../contexts/Web3Context';
import { useGame } from '../contexts/GameContext';
import { formatAddress, getExplorerUrl } from '../config/contracts';
import { WHEEL_SEGMENTS } from '../config/contracts';

const History = () => {
  const { account, isConnected, chainId } = useWeb3();
  const { userHistory, fetchUserHistory } = useGame();
  
  const [isLoading, setIsLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [filter, setFilter] = useState('all'); // all, wins, losses, pending

  useEffect(() => {
    if (account) {
      loadHistory(1, true);
    }
  }, [account, filter]);

  const loadHistory = async (page = 1, reset = false) => {
    if (!account) return;
    
    setIsLoading(true);
    try {
      const resolved = filter === 'pending' ? false : filter === 'all' ? undefined : true;
      const data = await fetchUserHistory(account, page, 20);
      
      if (data) {
        setHasMore(data.pagination.hasNextPage);
        if (reset) {
          setCurrentPage(1);
        } else {
          setCurrentPage(page);
        }
      }
    } catch (error) {
      console.error('Error loading history:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const loadMore = () => {
    if (!isLoading && hasMore) {
      loadHistory(currentPage + 1, false);
    }
  };

  const getSegmentInfo = (multiplier) => {
    const segment = WHEEL_SEGMENTS.find(s => s.multiplier === multiplier);
    return segment || { label: 'Unknown', color: '#666' };
  };

  const formatDate = (timestamp) => {
    return new Date(timestamp).toLocaleString();
  };

  const getFilteredHistory = () => {
    if (filter === 'all') return userHistory;
    if (filter === 'wins') return userHistory.filter(spin => spin.isWin);
    if (filter === 'losses') return userHistory.filter(spin => !spin.isWin && spin.isResolved);
    if (filter === 'pending') return userHistory.filter(spin => !spin.isResolved);
    return userHistory;
  };

  const filteredHistory = getFilteredHistory();

  if (!isConnected) {
    return (
      <div className="container" style={{ padding: '2rem 1rem' }}>
        <div className="text-center">
          <h1>📊 Spin History</h1>
          <div className="card" style={{ maxWidth: '500px', margin: '2rem auto' }}>
            <div style={{ padding: '3rem', textAlign: 'center' }}>
              <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>🔐</div>
              <h3>Connect Your Wallet</h3>
              <p style={{ opacity: 0.7, marginBottom: '2rem' }}>
                Connect your wallet to view your spin history
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container" style={{ padding: '2rem 1rem' }}>
      <div className="text-center mb-8">
        <h1>📊 Your Spin History</h1>
        <p className="text-lg opacity-60">
          Track all your spins and results
        </p>
      </div>

      {/* Filters */}
      <div className="card mb-8">
        <div style={{ display: 'flex', gap: '1rem', flexWrap: 'wrap' }}>
          {[
            { key: 'all', label: 'All Spins' },
            { key: 'wins', label: 'Wins' },
            { key: 'losses', label: 'Losses' },
            { key: 'pending', label: 'Pending' }
          ].map(({ key, label }) => (
            <button
              key={key}
              onClick={() => setFilter(key)}
              className={`btn ${filter === key ? 'btn-primary' : 'btn-secondary'}`}
            >
              {label}
            </button>
          ))}
        </div>
      </div>

      {/* History Table */}
      <div className="card">
        {isLoading && filteredHistory.length === 0 ? (
          <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
            {[...Array(5)].map((_, i) => (
              <div key={i} className="skeleton" style={{ height: '80px', borderRadius: '0.5rem' }} />
            ))}
          </div>
        ) : filteredHistory.length > 0 ? (
          <>
            {/* Desktop Table */}
            <div style={{ display: 'none' }} className="desktop-table">
              <table style={{ width: '100%', borderCollapse: 'collapse' }}>
                <thead>
                  <tr style={{ borderBottom: '1px solid rgba(255, 255, 255, 0.1)' }}>
                    <th style={{ padding: '1rem', textAlign: 'left' }}>Date</th>
                    <th style={{ padding: '1rem', textAlign: 'left' }}>Bet</th>
                    <th style={{ padding: '1rem', textAlign: 'left' }}>Result</th>
                    <th style={{ padding: '1rem', textAlign: 'left' }}>Payout</th>
                    <th style={{ padding: '1rem', textAlign: 'left' }}>Profit/Loss</th>
                    <th style={{ padding: '1rem', textAlign: 'left' }}>Status</th>
                    <th style={{ padding: '1rem', textAlign: 'left' }}>Tx</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredHistory.map((spin, index) => {
                    const segment = getSegmentInfo(spin.multiplier);
                    const isWin = spin.isWin;
                    
                    return (
                      <tr key={index} style={{ borderBottom: '1px solid rgba(255, 255, 255, 0.05)' }}>
                        <td style={{ padding: '1rem' }}>
                          <div style={{ fontSize: '0.875rem' }}>
                            {formatDate(spin.placedAt)}
                          </div>
                        </td>
                        <td style={{ padding: '1rem' }}>
                          <div style={{ fontWeight: '500' }}>
                            ${spin.betAmountUSD.toFixed(2)}
                          </div>
                        </td>
                        <td style={{ padding: '1rem' }}>
                          {spin.isResolved ? (
                            <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                              <div
                                style={{
                                  width: '12px',
                                  height: '12px',
                                  borderRadius: '50%',
                                  background: segment.color
                                }}
                              />
                              <span>{segment.label}</span>
                            </div>
                          ) : (
                            <span style={{ opacity: 0.6 }}>Pending...</span>
                          )}
                        </td>
                        <td style={{ padding: '1rem' }}>
                          <div style={{ fontWeight: '500' }}>
                            ${spin.payoutUSD.toFixed(2)}
                          </div>
                        </td>
                        <td style={{ padding: '1rem' }}>
                          {spin.isResolved && (
                            <div style={{
                              fontWeight: '500',
                              color: spin.profitLoss >= 0 ? '#10b981' : '#ef4444'
                            }}>
                              {spin.profitLoss >= 0 ? '+' : ''}${spin.profitLoss.toFixed(2)}
                            </div>
                          )}
                        </td>
                        <td style={{ padding: '1rem' }}>
                          <span style={{
                            padding: '0.25rem 0.5rem',
                            borderRadius: '0.25rem',
                            fontSize: '0.75rem',
                            background: spin.isResolved 
                              ? (isWin ? 'rgba(16, 185, 129, 0.2)' : 'rgba(239, 68, 68, 0.2)')
                              : 'rgba(251, 191, 36, 0.2)',
                            color: spin.isResolved 
                              ? (isWin ? '#10b981' : '#ef4444')
                              : '#f59e0b'
                          }}>
                            {spin.isResolved ? (isWin ? 'Won' : 'Lost') : 'Pending'}
                          </span>
                        </td>
                        <td style={{ padding: '1rem' }}>
                          <a
                            href={getExplorerUrl(chainId, spin.transactionHash)}
                            target="_blank"
                            rel="noopener noreferrer"
                            style={{
                              color: 'rgba(255, 255, 255, 0.6)',
                              textDecoration: 'none',
                              fontSize: '0.875rem'
                            }}
                          >
                            {formatAddress(spin.transactionHash, 6, 4)}
                          </a>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>

            {/* Mobile Cards */}
            <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
              {filteredHistory.map((spin, index) => {
                const segment = getSegmentInfo(spin.multiplier);
                const isWin = spin.isWin;
                
                return (
                  <div
                    key={index}
                    style={{
                      padding: '1rem',
                      background: 'rgba(255, 255, 255, 0.05)',
                      borderRadius: '0.5rem',
                      border: spin.isResolved 
                        ? `1px solid ${isWin ? 'rgba(16, 185, 129, 0.3)' : 'rgba(239, 68, 68, 0.3)'}`
                        : '1px solid rgba(251, 191, 36, 0.3)'
                    }}
                  >
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '0.5rem' }}>
                      <div style={{ fontSize: '0.875rem', opacity: 0.7 }}>
                        {formatDate(spin.placedAt)}
                      </div>
                      <span style={{
                        padding: '0.25rem 0.5rem',
                        borderRadius: '0.25rem',
                        fontSize: '0.75rem',
                        background: spin.isResolved 
                          ? (isWin ? 'rgba(16, 185, 129, 0.2)' : 'rgba(239, 68, 68, 0.2)')
                          : 'rgba(251, 191, 36, 0.2)',
                        color: spin.isResolved 
                          ? (isWin ? '#10b981' : '#ef4444')
                          : '#f59e0b'
                      }}>
                        {spin.isResolved ? (isWin ? 'Won' : 'Lost') : 'Pending'}
                      </span>
                    </div>
                    
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '0.5rem' }}>
                      <div>
                        <div style={{ fontSize: '0.875rem', opacity: 0.7 }}>Bet Amount</div>
                        <div style={{ fontWeight: '500' }}>${spin.betAmountUSD.toFixed(2)}</div>
                      </div>
                      <div style={{ textAlign: 'right' }}>
                        <div style={{ fontSize: '0.875rem', opacity: 0.7 }}>Result</div>
                        {spin.isResolved ? (
                          <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', justifyContent: 'flex-end' }}>
                            <div
                              style={{
                                width: '12px',
                                height: '12px',
                                borderRadius: '50%',
                                background: segment.color
                              }}
                            />
                            <span>{segment.label}</span>
                          </div>
                        ) : (
                          <span style={{ opacity: 0.6 }}>Pending...</span>
                        )}
                      </div>
                    </div>
                    
                    {spin.isResolved && (
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <div>
                          <div style={{ fontSize: '0.875rem', opacity: 0.7 }}>Payout</div>
                          <div style={{ fontWeight: '500' }}>${spin.payoutUSD.toFixed(2)}</div>
                        </div>
                        <div style={{ textAlign: 'right' }}>
                          <div style={{ fontSize: '0.875rem', opacity: 0.7 }}>Profit/Loss</div>
                          <div style={{
                            fontWeight: '500',
                            color: spin.profitLoss >= 0 ? '#10b981' : '#ef4444'
                          }}>
                            {spin.profitLoss >= 0 ? '+' : ''}${spin.profitLoss.toFixed(2)}
                          </div>
                        </div>
                      </div>
                    )}
                    
                    <div style={{ marginTop: '0.5rem', paddingTop: '0.5rem', borderTop: '1px solid rgba(255, 255, 255, 0.1)' }}>
                      <a
                        href={getExplorerUrl(chainId, spin.transactionHash)}
                        target="_blank"
                        rel="noopener noreferrer"
                        style={{
                          color: 'rgba(255, 255, 255, 0.6)',
                          textDecoration: 'none',
                          fontSize: '0.75rem'
                        }}
                      >
                        View Transaction: {formatAddress(spin.transactionHash, 8, 6)}
                      </a>
                    </div>
                  </div>
                );
              })}
            </div>

            {/* Load More Button */}
            {hasMore && (
              <div style={{ textAlign: 'center', marginTop: '2rem' }}>
                <button
                  onClick={loadMore}
                  disabled={isLoading}
                  className="btn btn-secondary"
                >
                  {isLoading ? (
                    <>
                      <div className="loading-spinner" />
                      Loading...
                    </>
                  ) : (
                    'Load More'
                  )}
                </button>
              </div>
            )}
          </>
        ) : (
          <div style={{ textAlign: 'center', padding: '3rem' }}>
            <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>🎰</div>
            <h3>No spins yet</h3>
            <p style={{ opacity: 0.7, marginBottom: '2rem' }}>
              {filter === 'all' 
                ? "You haven't placed any bets yet. Go to the game and start spinning!"
                : `No ${filter} found. Try a different filter.`
              }
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default History;
