import React from 'react';

const GameStats = ({ stats, userStats }) => {
  const formatNumber = (num) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num?.toFixed(0) || '0';
  };

  const formatCurrency = (amount) => {
    return parseFloat(amount || 0).toFixed(2);
  };

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
      {/* Global Stats */}
      <div className="card">
        <div className="card-header">
          <h3 className="card-title">🌍 Global Stats</h3>
        </div>
        
        {stats ? (
          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '1rem' }}>
            <div>
              <div className="text-sm opacity-60">Total Spins</div>
              <div className="text-xl font-bold">{formatNumber(stats.totalSpins)}</div>
            </div>
            <div>
              <div className="text-sm opacity-60">Total Volume</div>
              <div className="text-xl font-bold">${formatNumber(stats.totalVolumeUSD)}</div>
            </div>
            <div>
              <div className="text-sm opacity-60">Total Payouts</div>
              <div className="text-xl font-bold">${formatNumber(stats.totalPayoutsUSD)}</div>
            </div>
            <div>
              <div className="text-sm opacity-60">Unique Players</div>
              <div className="text-xl font-bold">{formatNumber(stats.uniquePlayers)}</div>
            </div>
            <div>
              <div className="text-sm opacity-60">Win Rate</div>
              <div className="text-xl font-bold">{stats.winRate?.toFixed(1)}%</div>
            </div>
            <div>
              <div className="text-sm opacity-60">House Edge</div>
              <div className="text-xl font-bold">{stats.houseEdge?.toFixed(1)}%</div>
            </div>
          </div>
        ) : (
          <div className="skeleton" style={{ height: '120px', borderRadius: '0.5rem' }} />
        )}
      </div>

      {/* Contract Info */}
      <div className="card">
        <div className="card-header">
          <h3 className="card-title">💰 Contract Info</h3>
        </div>
        
        {stats ? (
          <div style={{ display: 'flex', flexDirection: 'column', gap: '0.75rem' }}>
            <div className="flex justify-between">
              <span className="text-sm opacity-60">Contract Balance</span>
              <span className="font-bold">${formatCurrency(stats.contractBalanceUSD)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm opacity-60">Min Bet</span>
              <span className="font-bold">${formatCurrency(stats.minBetUSD)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm opacity-60">Max Bet</span>
              <span className="font-bold">${formatCurrency(stats.maxBetUSD)}</span>
            </div>
          </div>
        ) : (
          <div className="skeleton" style={{ height: '80px', borderRadius: '0.5rem' }} />
        )}
      </div>

      {/* User Stats */}
      {userStats && (
        <div className="card">
          <div className="card-header">
            <h3 className="card-title">👤 Your Stats</h3>
          </div>
          
          <div style={{ display: 'flex', flexDirection: 'column', gap: '0.75rem' }}>
            <div className="flex justify-between">
              <span className="text-sm opacity-60">Total Spins</span>
              <span className="font-bold">{userStats.totalSpins}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm opacity-60">Total Bet</span>
              <span className="font-bold">${formatCurrency(userStats.totalBet / 1e6)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm opacity-60">Total Payout</span>
              <span className="font-bold">${formatCurrency(userStats.totalPayout / 1e6)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm opacity-60">Profit/Loss</span>
              <span className={`font-bold ${userStats.profitLoss >= 0 ? 'text-success' : 'text-danger'}`}>
                {userStats.profitLoss >= 0 ? '+' : ''}${formatCurrency(userStats.profitLoss / 1e6)}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm opacity-60">Win Rate</span>
              <span className="font-bold">{userStats.winRate?.toFixed(1)}%</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm opacity-60">Biggest Win</span>
              <span className="font-bold">${formatCurrency(userStats.biggestWin / 1e6)}</span>
            </div>
          </div>
        </div>
      )}

      {/* Wheel Odds */}
      <div className="card">
        <div className="card-header">
          <h3 className="card-title">🎯 Wheel Odds</h3>
        </div>
        
        <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
          <div className="flex justify-between">
            <span className="text-sm">Lose (0×)</span>
            <span className="text-sm opacity-60">40%</span>
          </div>
          <div className="flex justify-between">
            <span className="text-sm">Win 1×</span>
            <span className="text-sm opacity-60">30%</span>
          </div>
          <div className="flex justify-between">
            <span className="text-sm">Win 2×</span>
            <span className="text-sm opacity-60">20%</span>
          </div>
          <div className="flex justify-between">
            <span className="text-sm">Win 5×</span>
            <span className="text-sm opacity-60">8%</span>
          </div>
          <div className="flex justify-between">
            <span className="text-sm">Win 10×</span>
            <span className="text-sm opacity-60">2%</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GameStats;
