import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useWeb3 } from '../contexts/Web3Context';
import { formatAddress } from '../config/contracts';

const Header = () => {
  const location = useLocation();
  const { 
    account, 
    isConnected, 
    isConnecting, 
    connectWallet, 
    disconnectWallet,
    balance,
    usdtBalance,
    networkName,
    isSupported
  } = useWeb3();
  
  const [showDropdown, setShowDropdown] = useState(false);

  const isActive = (path) => location.pathname === path;

  const handleConnect = async () => {
    await connectWallet(true);
  };

  const handleDisconnect = () => {
    disconnectWallet();
    setShowDropdown(false);
  };

  return (
    <header style={{
      background: 'rgba(255, 255, 255, 0.1)',
      backdropFilter: 'blur(10px)',
      borderBottom: '1px solid rgba(255, 255, 255, 0.2)',
      padding: '1rem 0',
      position: 'sticky',
      top: 0,
      zIndex: 100
    }}>
      <div className="container">
        <nav className="flex items-center justify-between">
          {/* Logo */}
          <Link 
            to="/" 
            style={{
              fontSize: '1.5rem',
              fontWeight: 'bold',
              textDecoration: 'none',
              color: 'white',
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem'
            }}
          >
            🎰 Spin-to-Win
          </Link>

          {/* Navigation Links */}
          <div className="flex items-center gap-8">
            <Link
              to="/"
              style={{
                textDecoration: 'none',
                color: isActive('/') ? '#667eea' : 'rgba(255, 255, 255, 0.8)',
                fontWeight: isActive('/') ? '600' : '400',
                transition: 'color 0.2s ease'
              }}
            >
              Game
            </Link>
            <Link
              to="/history"
              style={{
                textDecoration: 'none',
                color: isActive('/history') ? '#667eea' : 'rgba(255, 255, 255, 0.8)',
                fontWeight: isActive('/history') ? '600' : '400',
                transition: 'color 0.2s ease'
              }}
            >
              History
            </Link>
            <Link
              to="/stats"
              style={{
                textDecoration: 'none',
                color: isActive('/stats') ? '#667eea' : 'rgba(255, 255, 255, 0.8)',
                fontWeight: isActive('/stats') ? '600' : '400',
                transition: 'color 0.2s ease'
              }}
            >
              Stats
            </Link>
          </div>

          {/* Wallet Connection */}
          <div className="flex items-center gap-4">
            {isConnected ? (
              <div style={{ position: 'relative' }}>
                {/* Network Badge */}
                {!isSupported && (
                  <div style={{
                    background: '#ef4444',
                    color: 'white',
                    padding: '0.25rem 0.5rem',
                    borderRadius: '0.25rem',
                    fontSize: '0.75rem',
                    marginRight: '0.5rem'
                  }}>
                    Unsupported Network
                  </div>
                )}
                
                {/* Balance Display */}
                <div style={{
                  background: 'rgba(255, 255, 255, 0.1)',
                  padding: '0.5rem 1rem',
                  borderRadius: '0.5rem',
                  marginRight: '0.5rem',
                  fontSize: '0.875rem'
                }}>
                  <div>{parseFloat(usdtBalance).toFixed(2)} USDT</div>
                  <div style={{ opacity: 0.7, fontSize: '0.75rem' }}>
                    {parseFloat(balance).toFixed(4)} ETH
                  </div>
                </div>

                {/* Account Button */}
                <button
                  onClick={() => setShowDropdown(!showDropdown)}
                  className="btn btn-secondary"
                  style={{ position: 'relative' }}
                >
                  {formatAddress(account)}
                  <span style={{ marginLeft: '0.5rem' }}>▼</span>
                </button>

                {/* Dropdown Menu */}
                {showDropdown && (
                  <div style={{
                    position: 'absolute',
                    top: '100%',
                    right: 0,
                    marginTop: '0.5rem',
                    background: 'rgba(255, 255, 255, 0.1)',
                    backdropFilter: 'blur(10px)',
                    border: '1px solid rgba(255, 255, 255, 0.2)',
                    borderRadius: '0.5rem',
                    padding: '0.5rem',
                    minWidth: '200px',
                    zIndex: 1000
                  }}>
                    <div style={{
                      padding: '0.5rem',
                      borderBottom: '1px solid rgba(255, 255, 255, 0.1)',
                      marginBottom: '0.5rem'
                    }}>
                      <div style={{ fontSize: '0.875rem', opacity: 0.7 }}>
                        Connected to {networkName}
                      </div>
                      <div style={{ fontSize: '0.75rem', opacity: 0.6, marginTop: '0.25rem' }}>
                        {account}
                      </div>
                    </div>
                    
                    <button
                      onClick={handleDisconnect}
                      style={{
                        width: '100%',
                        padding: '0.5rem',
                        background: 'transparent',
                        border: 'none',
                        color: 'white',
                        cursor: 'pointer',
                        borderRadius: '0.25rem',
                        fontSize: '0.875rem'
                      }}
                      onMouseEnter={(e) => e.target.style.background = 'rgba(255, 255, 255, 0.1)'}
                      onMouseLeave={(e) => e.target.style.background = 'transparent'}
                    >
                      Disconnect Wallet
                    </button>
                  </div>
                )}
              </div>
            ) : (
              <button
                onClick={handleConnect}
                disabled={isConnecting}
                className="btn btn-primary"
              >
                {isConnecting ? (
                  <>
                    <div className="loading-spinner" />
                    Connecting...
                  </>
                ) : (
                  'Connect Wallet'
                )}
              </button>
            )}
          </div>
        </nav>
      </div>

      {/* Click outside to close dropdown */}
      {showDropdown && (
        <div
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            zIndex: 999
          }}
          onClick={() => setShowDropdown(false)}
        />
      )}
    </header>
  );
};

export default Header;
