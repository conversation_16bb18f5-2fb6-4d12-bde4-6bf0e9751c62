# Blockchain Configuration
PRIVATE_KEY=your_private_key_here
RPC_URL_MAINNET=https://mainnet.infura.io/v3/your_infura_key
RPC_URL_TESTNET=https://sepolia.infura.io/v3/your_infura_key
RPC_URL_BSC_MAINNET=https://bsc-dataseed1.binance.org/
RPC_URL_BSC_TESTNET=https://data-seed-prebsc-1-s1.binance.org:8545/

# Chainlink VRF Configuration
CHAINLINK_VRF_COORDINATOR=0x8103B0A8A00be2DDC778e6e7eaa21791Cd364625
CHAINLINK_KEY_HASH=0x474e34a077df58807dbe9c96d3c009b23b3c6d0cce433e59bbf5b34f823bc56c
CHAINLINK_SUBSCRIPTION_ID=your_subscription_id

# Contract Addresses
USDT_CONTRACT_ADDRESS=******************************************
SPIN_TO_WIN_CONTRACT_ADDRESS=your_deployed_contract_address

# Database
MONGO_URI=mongodb://localhost:27017/spin-to-win
REDIS_URL=redis://localhost:6379

# API Configuration
PORT=3001
JWT_SECRET=your_jwt_secret_here
API_BASE_URL=http://localhost:3001

# Frontend Configuration
REACT_APP_API_URL=http://localhost:3001
REACT_APP_CHAIN_ID=1
REACT_APP_USDT_ADDRESS=******************************************
REACT_APP_CONTRACT_ADDRESS=your_deployed_contract_address

# External APIs
ETHERSCAN_API_KEY=your_etherscan_api_key
BSCSCAN_API_KEY=your_bscscan_api_key

# Development
NODE_ENV=development
LOG_LEVEL=debug
