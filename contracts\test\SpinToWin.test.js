const { expect } = require("chai");
const { ethers } = require("hardhat");

describe("SpinToWin", function () {
  let spinToWin;
  let mockUSDT;
  let mockVRFCoordinator;
  let owner;
  let player1;
  let player2;

  const MINIMUM_BET = ethers.utils.parseUnits("2", 6); // 2 USDT
  const SUBSCRIPTION_ID = 1;
  const KEY_HASH = "0x474e34a077df58807dbe9c96d3c009b23b3c6d0cce433e59bbf5b34f823bc56c";

  beforeEach(async function () {
    [owner, player1, player2] = await ethers.getSigners();

    // Deploy mock USDT token
    const MockERC20 = await ethers.getContractFactory("MockERC20");
    mockUSDT = await MockERC20.deploy("Mock USDT", "USDT", 6);
    await mockUSDT.deployed();

    // Deploy mock VRF Coordinator
    const MockVRFCoordinator = await ethers.getContractFactory("MockVRFCoordinator");
    mockVRFCoordinator = await MockVRFCoordinator.deploy();
    await mockVRFCoordinator.deployed();

    // Deploy SpinToWin contract
    const SpinToWin = await ethers.getContractFactory("SpinToWin");
    spinToWin = await SpinToWin.deploy(
      mockVRFCoordinator.address,
      SUBSCRIPTION_ID,
      KEY_HASH,
      mockUSDT.address
    );
    await spinToWin.deployed();

    // Mint USDT to players
    await mockUSDT.mint(player1.address, ethers.utils.parseUnits("1000", 6));
    await mockUSDT.mint(player2.address, ethers.utils.parseUnits("1000", 6));

    // Fund contract with USDT for payouts
    await mockUSDT.mint(spinToWin.address, ethers.utils.parseUnits("10000", 6));

    // Approve contract to spend player's USDT
    await mockUSDT.connect(player1).approve(spinToWin.address, ethers.constants.MaxUint256);
    await mockUSDT.connect(player2).approve(spinToWin.address, ethers.constants.MaxUint256);
  });

  describe("Deployment", function () {
    it("Should set the correct initial values", async function () {
      expect(await spinToWin.usdtToken()).to.equal(mockUSDT.address);
      expect(await spinToWin.MINIMUM_BET()).to.equal(MINIMUM_BET);
      expect(await spinToWin.totalSpins()).to.equal(0);
      expect(await spinToWin.totalVolume()).to.equal(0);
      expect(await spinToWin.totalPayouts()).to.equal(0);
    });

    it("Should have correct wheel configuration", async function () {
      const [multipliers, odds] = await spinToWin.getWheelConfig();
      expect(multipliers).to.deep.equal([0, 1, 2, 5, 10]);
      expect(odds).to.deep.equal([4000, 3000, 2000, 800, 200]);
    });
  });

  describe("Betting", function () {
    it("Should allow valid bets", async function () {
      const betAmount = MINIMUM_BET;

      await expect(spinToWin.connect(player1).placeBet(betAmount))
        .to.emit(spinToWin, "BetPlaced")
        .withArgs(player1.address, 1, betAmount, await getBlockTimestamp());

      expect(await spinToWin.totalSpins()).to.equal(1);
      expect(await spinToWin.totalVolume()).to.equal(betAmount);
    });

    it("Should reject bets below minimum", async function () {
      const betAmount = MINIMUM_BET.sub(1);

      await expect(spinToWin.connect(player1).placeBet(betAmount))
        .to.be.revertedWith("Bet amount too low");
    });

    it("Should reject bets above maximum", async function () {
      const maxBet = await spinToWin.getMaxBet();
      const betAmount = maxBet.add(1);

      await expect(spinToWin.connect(player1).placeBet(betAmount))
        .to.be.revertedWith("Bet amount too high");
    });

    it("Should transfer USDT from player", async function () {
      const betAmount = MINIMUM_BET;
      const initialBalance = await mockUSDT.balanceOf(player1.address);

      await spinToWin.connect(player1).placeBet(betAmount);

      const finalBalance = await mockUSDT.balanceOf(player1.address);
      expect(finalBalance).to.equal(initialBalance.sub(betAmount));
    });
  });

  describe("VRF Fulfillment", function () {
    it("Should fulfill randomness and determine result", async function () {
      const betAmount = MINIMUM_BET;

      // Place bet
      await spinToWin.connect(player1).placeBet(betAmount);

      // Simulate VRF response with a winning result (segment 4 = 10x multiplier)
      const randomWord = 9900; // This should land on the 10x segment
      await mockVRFCoordinator.fulfillRandomWords(1, [randomWord]);

      const spin = await spinToWin.getSpin(1);
      expect(spin.fulfilled).to.be.true;
      expect(spin.result).to.equal(4); // 10x multiplier segment
      expect(spin.payout).to.equal(betAmount.mul(10));
    });

    it("Should pay out winnings correctly", async function () {
      const betAmount = MINIMUM_BET;
      const initialBalance = await mockUSDT.balanceOf(player1.address);

      // Place bet
      await spinToWin.connect(player1).placeBet(betAmount);

      // Simulate VRF response with 2x multiplier
      const randomWord = 7000; // This should land on the 2x segment
      await mockVRFCoordinator.fulfillRandomWords(1, [randomWord]);

      const finalBalance = await mockUSDT.balanceOf(player1.address);
      const expectedBalance = initialBalance.sub(betAmount).add(betAmount.mul(2));
      expect(finalBalance).to.equal(expectedBalance);
    });

    it("Should handle losing spins correctly", async function () {
      const betAmount = MINIMUM_BET;
      const initialBalance = await mockUSDT.balanceOf(player1.address);

      // Place bet
      await spinToWin.connect(player1).placeBet(betAmount);

      // Simulate VRF response with losing result
      const randomWord = 2000; // This should land on the lose segment
      await mockVRFCoordinator.fulfillRandomWords(1, [randomWord]);

      const finalBalance = await mockUSDT.balanceOf(player1.address);
      expect(finalBalance).to.equal(initialBalance.sub(betAmount));

      const spin = await spinToWin.getSpin(1);
      expect(spin.payout).to.equal(0);
    });
  });

  describe("Owner Functions", function () {
    it("Should allow owner to update wheel configuration", async function () {
      const newMultipliers = [0, 1, 3, 7, 15];
      const newOdds = [5000, 2500, 1500, 800, 200];

      await expect(spinToWin.setWheelSegments(newMultipliers, newOdds))
        .to.emit(spinToWin, "WheelConfigUpdated")
        .withArgs(newMultipliers, newOdds);

      const [multipliers, odds] = await spinToWin.getWheelConfig();
      expect(multipliers).to.deep.equal(newMultipliers);
      expect(odds).to.deep.equal(newOdds);
    });

    it("Should reject invalid wheel configuration", async function () {
      const invalidOdds = [5000, 2500, 1500, 800]; // Doesn't sum to 10000
      const multipliers = [0, 1, 3, 7, 15];

      await expect(spinToWin.setWheelSegments(multipliers, invalidOdds))
        .to.be.revertedWith("Odds must sum to 10000");
    });

    it("Should allow owner to withdraw treasury", async function () {
      const withdrawAmount = ethers.utils.parseUnits("100", 6);

      await expect(spinToWin.withdrawTreasury(owner.address, withdrawAmount))
        .to.emit(spinToWin, "TreasuryWithdrawn")
        .withArgs(owner.address, withdrawAmount);
    });

    it("Should reject non-owner calls", async function () {
      await expect(spinToWin.connect(player1).pause())
        .to.be.revertedWith("Ownable: caller is not the owner");
    });
  });

  describe("Pausable", function () {
    it("Should allow owner to pause and unpause", async function () {
      await spinToWin.pause();

      await expect(spinToWin.connect(player1).placeBet(MINIMUM_BET))
        .to.be.revertedWith("Pausable: paused");

      await spinToWin.unpause();

      await expect(spinToWin.connect(player1).placeBet(MINIMUM_BET))
        .to.emit(spinToWin, "BetPlaced");
    });
  });

  describe("Edge Cases", function () {
    it("Should handle maximum bet correctly", async function () {
      const maxBet = await spinToWin.getMaxBet();

      await expect(spinToWin.connect(player1).placeBet(maxBet))
        .to.emit(spinToWin, "BetPlaced");
    });

    it("Should prevent double spending", async function () {
      const betAmount = MINIMUM_BET;

      // Place first bet
      await spinToWin.connect(player1).placeBet(betAmount);

      // Try to place second bet immediately (should work if player has enough balance)
      await expect(spinToWin.connect(player1).placeBet(betAmount))
        .to.emit(spinToWin, "BetPlaced");
    });

    it("Should handle contract with zero balance", async function () {
      // Deploy new contract with no funding
      const SpinToWin = await ethers.getContractFactory("SpinToWin");
      const emptyContract = await SpinToWin.deploy(
        mockVRFCoordinator.address,
        SUBSCRIPTION_ID,
        KEY_HASH,
        mockUSDT.address
      );

      const maxBet = await emptyContract.getMaxBet();
      expect(maxBet).to.equal(0);
    });
  });

  describe("Gas Optimization", function () {
    it("Should use reasonable gas for placing bets", async function () {
      const betAmount = MINIMUM_BET;
      const tx = await spinToWin.connect(player1).placeBet(betAmount);
      const receipt = await tx.wait();

      // Gas should be reasonable (less than 200k)
      expect(receipt.gasUsed.toNumber()).to.be.lessThan(200000);
    });
  });

  async function getBlockTimestamp() {
    const block = await ethers.provider.getBlock("latest");
    return block.timestamp;
  }
});
