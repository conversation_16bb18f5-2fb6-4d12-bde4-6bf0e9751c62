{"name": "spin-to-win-dapp", "version": "1.0.0", "description": "A provably fair Web3 gambling application", "main": "index.js", "scripts": {"install:all": "npm install && cd contracts && npm install && cd ../backend && npm install && cd ../frontend && npm install", "dev": "concurrently \"cd backend && npm run dev\" \"cd frontend && npm start\"", "build": "cd contracts && npm run compile && cd ../backend && npm run build && cd ../frontend && npm run build", "test": "cd contracts && npm test && cd ../backend && npm test && cd ../frontend && npm test", "deploy:testnet": "cd contracts && npm run deploy:testnet", "deploy:mainnet": "cd contracts && npm run deploy:mainnet", "lint": "cd contracts && npm run lint && cd ../backend && npm run lint && cd ../frontend && npm run lint", "clean": "cd contracts && npm run clean && cd ../backend && npm run clean && cd ../frontend && npm run clean"}, "keywords": ["web3", "dapp", "gambling", "chainlink", "vrf", "ethereum", "bsc", "usdt"], "author": "Your Name", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}, "engines": {"node": ">=18.0.0"}}