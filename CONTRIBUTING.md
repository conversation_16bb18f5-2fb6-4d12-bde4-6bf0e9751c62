# Contributing to Spin-to-Win dApp

Thank you for your interest in contributing to the Spin-to-Win dApp! This document provides guidelines and information for contributors.

## Table of Contents

- [Code of Conduct](#code-of-conduct)
- [Getting Started](#getting-started)
- [Development Setup](#development-setup)
- [Contributing Guidelines](#contributing-guidelines)
- [Pull Request Process](#pull-request-process)
- [Coding Standards](#coding-standards)
- [Testing](#testing)
- [Security](#security)

## Code of Conduct

By participating in this project, you agree to abide by our Code of Conduct:

- Be respectful and inclusive
- Focus on constructive feedback
- Help others learn and grow
- Maintain professionalism in all interactions

## Getting Started

### Prerequisites

- Node.js 18+
- Git
- Docker and Docker Compose
- Basic knowledge of:
  - Solidity and smart contracts
  - React and JavaScript
  - Node.js and Express
  - MongoDB

### Development Setup

1. **Fork and clone the repository**:
```bash
git clone https://github.com/your-username/spin-to-win-dapp.git
cd spin-to-win-dapp
```

2. **Install dependencies**:
```bash
make install
# or
npm run install:all
```

3. **Set up environment**:
```bash
cp .env.example .env
# Edit .env with your configuration
```

4. **Start development services**:
```bash
make dev-db
```

5. **Run the application**:
```bash
# Terminal 1: Backend
cd backend && npm run dev

# Terminal 2: Frontend
cd frontend && npm start
```

## Contributing Guidelines

### Types of Contributions

We welcome the following types of contributions:

- 🐛 **Bug fixes**
- ✨ **New features**
- 📚 **Documentation improvements**
- 🧪 **Test coverage improvements**
- 🔧 **Performance optimizations**
- 🎨 **UI/UX improvements**
- 🔒 **Security enhancements**

### Before You Start

1. **Check existing issues** to avoid duplicate work
2. **Create an issue** for new features or major changes
3. **Discuss your approach** in the issue before implementing

### Branch Naming Convention

Use descriptive branch names with prefixes:

- `feature/add-new-wheel-segment`
- `bugfix/fix-balance-calculation`
- `docs/update-api-documentation`
- `test/add-contract-tests`
- `refactor/optimize-database-queries`

## Pull Request Process

### 1. Create a Feature Branch

```bash
git checkout -b feature/your-feature-name
```

### 2. Make Your Changes

- Follow the coding standards
- Add tests for new functionality
- Update documentation as needed
- Ensure all tests pass

### 3. Test Your Changes

```bash
# Run all tests
make test

# Run specific tests
make test-contracts
make test-backend
make test-frontend

# Run linting
make lint
```

### 4. Commit Your Changes

Use conventional commit messages:

```bash
git commit -m "feat: add new wheel segment configuration"
git commit -m "fix: resolve balance calculation issue"
git commit -m "docs: update API documentation"
git commit -m "test: add unit tests for spin validation"
```

### 5. Push and Create PR

```bash
git push origin feature/your-feature-name
```

Then create a Pull Request with:

- **Clear title** describing the change
- **Detailed description** of what was changed and why
- **Screenshots** for UI changes
- **Testing instructions** for reviewers
- **Link to related issues**

### 6. Address Review Feedback

- Respond to comments promptly
- Make requested changes
- Update tests if needed
- Re-request review when ready

## Coding Standards

### Smart Contracts (Solidity)

- Use Solidity 0.8.20+
- Follow OpenZeppelin patterns
- Include comprehensive NatSpec documentation
- Use descriptive variable and function names
- Implement proper access controls
- Add input validation
- Use events for important state changes

```solidity
/**
 * @dev Places a bet and requests randomness from Chainlink VRF
 * @param amount The bet amount in USDT (with 6 decimals)
 */
function placeBet(uint256 amount) external nonReentrant whenNotPaused {
    require(amount >= MINIMUM_BET, "Bet amount too low");
    // Implementation...
}
```

### Backend (Node.js)

- Use ES6+ features
- Follow RESTful API conventions
- Implement proper error handling
- Use async/await for asynchronous operations
- Validate all inputs
- Include comprehensive logging

```javascript
const placeBet = async (req, res) => {
  try {
    const { error, value } = placeBetSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        error: 'Validation error',
        details: error.details[0].message
      });
    }
    // Implementation...
  } catch (error) {
    logger.error('Error placing bet:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};
```

### Frontend (React)

- Use functional components with hooks
- Implement proper error boundaries
- Use TypeScript-style prop validation
- Follow React best practices
- Implement responsive design
- Use semantic HTML

```jsx
const SpinButton = ({ amount, onSpin, disabled }) => {
  const handleClick = useCallback(() => {
    if (!disabled && amount > 0) {
      onSpin(amount);
    }
  }, [amount, onSpin, disabled]);

  return (
    <button 
      onClick={handleClick}
      disabled={disabled}
      className="btn btn-primary"
    >
      {disabled ? 'Spinning...' : `Spin for ${amount} USDT`}
    </button>
  );
};
```

### General Guidelines

- **Comments**: Write clear, concise comments for complex logic
- **Naming**: Use descriptive names for variables, functions, and classes
- **Functions**: Keep functions small and focused on a single responsibility
- **Error Handling**: Always handle errors gracefully
- **Security**: Never expose sensitive information
- **Performance**: Consider performance implications of your changes

## Testing

### Test Requirements

All contributions must include appropriate tests:

- **Smart Contracts**: Unit tests for all functions
- **Backend**: API endpoint tests and unit tests
- **Frontend**: Component tests and integration tests

### Test Coverage

Maintain high test coverage:

- Smart contracts: >90%
- Backend: >80%
- Frontend: >70%

### Running Tests

```bash
# All tests
make test

# With coverage
cd backend && npm run test:coverage
cd frontend && npm test -- --coverage
cd contracts && npm run coverage
```

## Security

### Security Considerations

- **Smart Contracts**: Follow security best practices
- **API Security**: Validate all inputs, use rate limiting
- **Frontend Security**: Sanitize user inputs, use HTTPS
- **Dependencies**: Keep dependencies updated

### Reporting Security Issues

For security vulnerabilities, <NAME_EMAIL> instead of creating a public issue.

## Documentation

### Documentation Requirements

- Update README.md for new features
- Add JSDoc comments for functions
- Update API documentation
- Include inline comments for complex logic

### Documentation Style

- Use clear, concise language
- Include code examples
- Provide step-by-step instructions
- Keep documentation up-to-date

## Questions and Support

- **General Questions**: Create a GitHub issue with the "question" label
- **Bug Reports**: Use the bug report template
- **Feature Requests**: Use the feature request template
- **Security Issues**: Email <EMAIL>

## Recognition

Contributors will be recognized in:

- README.md contributors section
- Release notes for significant contributions
- Special recognition for security improvements

Thank you for contributing to Spin-to-Win dApp! 🎰
