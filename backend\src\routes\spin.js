const express = require('express');
const Joi = require('joi');
const contractService = require('../services/contractService');
const Spin = require('../models/Spin');
const logger = require('../utils/logger');

const router = express.Router();

// Validation schemas
const placeBetSchema = Joi.object({
  amount: Joi.string().required(),
  playerAddress: Joi.string().pattern(/^0x[a-fA-F0-9]{40}$/).required()
});

const getResultSchema = Joi.object({
  txHash: Joi.string().pattern(/^0x[a-fA-F0-9]{64}$/).required()
});

/**
 * POST /api/spin
 * Place a bet and spin the wheel
 */
router.post('/', async (req, res) => {
  try {
    // Validate request
    const { error, value } = placeBetSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        error: 'Validation error',
        details: error.details[0].message
      });
    }

    const { amount, playerAddress } = value;

    // Validate bet amount
    const validation = await contractService.validateBetAmount(amount);
    if (!validation.valid) {
      return res.status(400).json({
        error: 'Invalid bet amount',
        details: validation.error
      });
    }

    // Check player balance and allowance
    const [balance, allowance] = await Promise.all([
      contractService.getPlayerBalance(playerAddress),
      contractService.getPlayerAllowance(playerAddress)
    ]);

    if (BigInt(balance) < BigInt(amount)) {
      return res.status(400).json({
        error: 'Insufficient balance',
        details: 'Player does not have enough USDT'
      });
    }

    if (BigInt(allowance) < BigInt(amount)) {
      return res.status(400).json({
        error: 'Insufficient allowance',
        details: 'Player must approve the contract to spend USDT'
      });
    }

    // Return success response with instructions for frontend
    res.json({
      success: true,
      message: 'Validation passed. Please confirm the transaction in your wallet.',
      data: {
        amount,
        playerAddress,
        contractAddress: process.env.SPIN_TO_WIN_CONTRACT_ADDRESS,
        minBet: await contractService.spinToWinContract.MINIMUM_BET(),
        maxBet: await contractService.getContractStats().then(stats => stats.maxBet)
      }
    });

  } catch (error) {
    logger.error('Error in spin endpoint:', error);
    res.status(500).json({
      error: 'Internal server error',
      details: 'Failed to process spin request'
    });
  }
});

/**
 * GET /api/spin/result/:txHash
 * Get spin result by transaction hash
 */
router.get('/result/:txHash', async (req, res) => {
  try {
    // Validate request
    const { error, value } = getResultSchema.validate(req.params);
    if (error) {
      return res.status(400).json({
        error: 'Validation error',
        details: error.details[0].message
      });
    }

    const { txHash } = value;

    // Find spin by transaction hash
    const spin = await Spin.findOne({ transactionHash: txHash });
    if (!spin) {
      return res.status(404).json({
        error: 'Spin not found',
        details: 'No spin found with the provided transaction hash'
      });
    }

    // Return spin data
    res.json({
      success: true,
      data: {
        requestId: spin.requestId,
        playerAddress: spin.playerAddress,
        betAmount: spin.betAmount,
        betAmountUSD: spin.betAmountUSD,
        isResolved: spin.isResolved,
        segmentIndex: spin.segmentIndex,
        multiplier: spin.multiplier,
        payout: spin.payout,
        payoutUSD: spin.payoutUSD,
        profitLoss: spin.profitLoss,
        isWin: spin.isWin,
        placedAt: spin.placedAt,
        resolvedAt: spin.resolvedAt,
        transactionHash: spin.transactionHash,
        blockNumber: spin.blockNumber
      }
    });

  } catch (error) {
    logger.error('Error getting spin result:', error);
    res.status(500).json({
      error: 'Internal server error',
      details: 'Failed to get spin result'
    });
  }
});

/**
 * GET /api/spin/pending/:address
 * Get pending spins for a player
 */
router.get('/pending/:address', async (req, res) => {
  try {
    const playerAddress = req.params.address.toLowerCase();
    
    // Validate address format
    if (!/^0x[a-fA-F0-9]{40}$/.test(playerAddress)) {
      return res.status(400).json({
        error: 'Invalid address format'
      });
    }

    // Find pending spins
    const pendingSpins = await Spin.find({
      playerAddress,
      isResolved: false
    }).sort({ placedAt: -1 });

    res.json({
      success: true,
      data: pendingSpins.map(spin => ({
        requestId: spin.requestId,
        betAmount: spin.betAmount,
        betAmountUSD: spin.betAmountUSD,
        placedAt: spin.placedAt,
        transactionHash: spin.transactionHash
      }))
    });

  } catch (error) {
    logger.error('Error getting pending spins:', error);
    res.status(500).json({
      error: 'Internal server error',
      details: 'Failed to get pending spins'
    });
  }
});

/**
 * GET /api/spin/validate/:amount
 * Validate bet amount without placing bet
 */
router.get('/validate/:amount', async (req, res) => {
  try {
    const amount = req.params.amount;
    
    // Validate amount format
    if (!/^\d+$/.test(amount)) {
      return res.status(400).json({
        error: 'Invalid amount format'
      });
    }

    const validation = await contractService.validateBetAmount(amount);
    
    if (validation.valid) {
      const stats = await contractService.getContractStats();
      res.json({
        success: true,
        valid: true,
        data: {
          amount,
          minBet: stats.minBet,
          maxBet: stats.maxBet
        }
      });
    } else {
      res.json({
        success: true,
        valid: false,
        error: validation.error
      });
    }

  } catch (error) {
    logger.error('Error validating bet amount:', error);
    res.status(500).json({
      error: 'Internal server error',
      details: 'Failed to validate bet amount'
    });
  }
});

module.exports = router;
