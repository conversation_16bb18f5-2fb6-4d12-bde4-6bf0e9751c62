import React, { createContext, useContext, useState, useEffect } from 'react';
import toast from 'react-hot-toast';

import { useWeb3 } from './Web3Context';
import { API_BASE_URL } from '../config/contracts';

const GameContext = createContext();

export const useGame = () => {
  const context = useContext(GameContext);
  if (!context) {
    throw new Error('useGame must be used within a GameProvider');
  }
  return context;
};

export const GameProvider = ({ children }) => {
  const { account, spinToWinContract, isConnected } = useWeb3();
  
  const [gameStats, setGameStats] = useState(null);
  const [userStats, setUserStats] = useState(null);
  const [userHistory, setUserHistory] = useState([]);
  const [pendingSpins, setPendingSpins] = useState([]);
  const [recentSpins, setRecentSpins] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [lastUpdate, setLastUpdate] = useState(null);

  // Fetch game statistics
  const fetchGameStats = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/stats`);
      const data = await response.json();
      
      if (data.success) {
        setGameStats(data.data);
      }
    } catch (error) {
      console.error('Error fetching game stats:', error);
    }
  };

  // Fetch user statistics
  const fetchUserStats = async (address) => {
    try {
      if (!address) return;
      
      const response = await fetch(`${API_BASE_URL}/api/user/stats/${address}`);
      const data = await response.json();
      
      if (data.success) {
        setUserStats(data.data);
      }
    } catch (error) {
      console.error('Error fetching user stats:', error);
    }
  };

  // Fetch user history
  const fetchUserHistory = async (address, page = 1, limit = 20) => {
    try {
      if (!address) return;
      
      const response = await fetch(`${API_BASE_URL}/api/user/history/${address}?page=${page}&limit=${limit}`);
      const data = await response.json();
      
      if (data.success) {
        if (page === 1) {
          setUserHistory(data.data.spins);
        } else {
          setUserHistory(prev => [...prev, ...data.data.spins]);
        }
        return data.data;
      }
    } catch (error) {
      console.error('Error fetching user history:', error);
    }
  };

  // Fetch pending spins
  const fetchPendingSpins = async (address) => {
    try {
      if (!address) return;
      
      const response = await fetch(`${API_BASE_URL}/api/spin/pending/${address}`);
      const data = await response.json();
      
      if (data.success) {
        setPendingSpins(data.data);
      }
    } catch (error) {
      console.error('Error fetching pending spins:', error);
    }
  };

  // Fetch recent spins
  const fetchRecentSpins = async (limit = 20) => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/stats/recent-spins?limit=${limit}`);
      const data = await response.json();
      
      if (data.success) {
        setRecentSpins(data.data);
      }
    } catch (error) {
      console.error('Error fetching recent spins:', error);
    }
  };

  // Get spin result by transaction hash
  const getSpinResult = async (txHash) => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/spin/result/${txHash}`);
      const data = await response.json();
      
      if (data.success) {
        return data.data;
      }
      return null;
    } catch (error) {
      console.error('Error fetching spin result:', error);
      return null;
    }
  };

  // Validate bet amount
  const validateBetAmount = async (amount) => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/spin/validate/${amount}`);
      const data = await response.json();
      
      if (data.success) {
        return data;
      }
      return { valid: false, error: 'Validation failed' };
    } catch (error) {
      console.error('Error validating bet amount:', error);
      return { valid: false, error: 'Validation failed' };
    }
  };

  // Place a bet
  const placeBet = async (amount) => {
    try {
      setIsLoading(true);
      
      // Validate bet amount first
      const validation = await validateBetAmount(amount);
      if (!validation.valid) {
        toast.error(validation.error);
        return null;
      }

      // Validate with backend
      const response = await fetch(`${API_BASE_URL}/api/spin`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount: (parseFloat(amount) * 1e6).toString(), // Convert to wei
          playerAddress: account
        })
      });

      const data = await response.json();
      
      if (!data.success) {
        toast.error(data.details || 'Failed to validate bet');
        return null;
      }

      // If validation passes, the frontend should call the contract directly
      return data;
    } catch (error) {
      console.error('Error placing bet:', error);
      toast.error('Failed to place bet');
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  // Refresh all data
  const refreshData = async () => {
    setIsLoading(true);
    try {
      await Promise.all([
        fetchGameStats(),
        account && fetchUserStats(account),
        account && fetchUserHistory(account),
        account && fetchPendingSpins(account),
        fetchRecentSpins()
      ]);
      setLastUpdate(new Date());
    } catch (error) {
      console.error('Error refreshing data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Auto-refresh data
  useEffect(() => {
    refreshData();
    
    // Set up auto-refresh every 30 seconds
    const interval = setInterval(refreshData, 30000);
    
    return () => clearInterval(interval);
  }, [account, isConnected]);

  // Listen for contract events
  useEffect(() => {
    if (!spinToWinContract || !account) return;

    const handleBetPlaced = (player, requestId, betAmount, timestamp, event) => {
      if (player.toLowerCase() === account.toLowerCase()) {
        toast.success('Bet placed! Waiting for result...');
        refreshData();
      }
    };

    const handleSpinResult = (player, requestId, betAmount, result, multiplier, payout, timestamp, event) => {
      if (player.toLowerCase() === account.toLowerCase()) {
        const multiplierNum = multiplier.toNumber();
        if (multiplierNum > 0) {
          toast.success(`🎉 You won ${multiplierNum}× your bet!`);
        } else {
          toast.error('Better luck next time!');
        }
        refreshData();
      }
    };

    // Set up event listeners
    spinToWinContract.on('BetPlaced', handleBetPlaced);
    spinToWinContract.on('SpinResult', handleSpinResult);

    return () => {
      spinToWinContract.removeAllListeners('BetPlaced');
      spinToWinContract.removeAllListeners('SpinResult');
    };
  }, [spinToWinContract, account]);

  const value = {
    // State
    gameStats,
    userStats,
    userHistory,
    pendingSpins,
    recentSpins,
    isLoading,
    lastUpdate,

    // Actions
    fetchGameStats,
    fetchUserStats,
    fetchUserHistory,
    fetchPendingSpins,
    fetchRecentSpins,
    getSpinResult,
    validateBetAmount,
    placeBet,
    refreshData,

    // Utils
    hasData: gameStats !== null,
    hasPendingSpins: pendingSpins.length > 0,
  };

  return (
    <GameContext.Provider value={value}>
      {children}
    </GameContext.Provider>
  );
};
