version: '3.8'

services:
  # MongoDB Database
  mongodb:
    image: mongo:7
    container_name: spin-to-win-mongodb
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: ${MONGO_ROOT_USERNAME:-admin}
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_ROOT_PASSWORD:-password}
      MONGO_INITDB_DATABASE: ${MONGO_DB_NAME:-spin-to-win}
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
      - mongodb_config:/data/configdb
    networks:
      - spin-to-win-network

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: spin-to-win-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - spin-to-win-network
    command: redis-server --appendonly yes

  # Backend API
  backend:
    build:
      context: .
      dockerfile: docker/backend.Dockerfile
    container_name: spin-to-win-backend
    restart: unless-stopped
    environment:
      NODE_ENV: ${NODE_ENV:-production}
      PORT: 3001
      MONGO_URI: mongodb://${MONGO_ROOT_USERNAME:-admin}:${MONGO_ROOT_PASSWORD:-password}@mongodb:27017/${MONGO_DB_NAME:-spin-to-win}?authSource=admin
      REDIS_URL: redis://redis:6379
      PRIVATE_KEY: ${PRIVATE_KEY}
      RPC_URL_MAINNET: ${RPC_URL_MAINNET}
      RPC_URL_TESTNET: ${RPC_URL_TESTNET}
      CHAINLINK_VRF_COORDINATOR: ${CHAINLINK_VRF_COORDINATOR}
      CHAINLINK_KEY_HASH: ${CHAINLINK_KEY_HASH}
      CHAINLINK_SUBSCRIPTION_ID: ${CHAINLINK_SUBSCRIPTION_ID}
      USDT_CONTRACT_ADDRESS: ${USDT_CONTRACT_ADDRESS}
      SPIN_TO_WIN_CONTRACT_ADDRESS: ${SPIN_TO_WIN_CONTRACT_ADDRESS}
      JWT_SECRET: ${JWT_SECRET}
    ports:
      - "3001:3001"
    depends_on:
      - mongodb
      - redis
    networks:
      - spin-to-win-network
    volumes:
      - ./backend/logs:/app/logs

  # Frontend
  frontend:
    build:
      context: .
      dockerfile: docker/frontend.Dockerfile
      args:
        REACT_APP_API_URL: ${REACT_APP_API_URL:-http://localhost:3001}
        REACT_APP_CONTRACT_ADDRESS: ${REACT_APP_CONTRACT_ADDRESS}
        REACT_APP_CHAIN_ID: ${REACT_APP_CHAIN_ID:-1}
    container_name: spin-to-win-frontend
    restart: unless-stopped
    ports:
      - "3000:3000"
    depends_on:
      - backend
    networks:
      - spin-to-win-network

  # Nginx Reverse Proxy (Optional)
  nginx:
    image: nginx:alpine
    container_name: spin-to-win-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./docker/nginx-proxy.conf:/etc/nginx/nginx.conf:ro
      - ./docker/ssl:/etc/nginx/ssl:ro
    depends_on:
      - frontend
      - backend
    networks:
      - spin-to-win-network
    profiles:
      - production

volumes:
  mongodb_data:
    driver: local
  mongodb_config:
    driver: local
  redis_data:
    driver: local

networks:
  spin-to-win-network:
    driver: bridge
