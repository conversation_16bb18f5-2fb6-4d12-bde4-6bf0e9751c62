name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  release:
    types: [ published ]

env:
  NODE_VERSION: '18'
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  # Lint and Test
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        component: [contracts, backend, frontend]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: ${{ matrix.component }}/package-lock.json

    - name: Install dependencies
      run: |
        cd ${{ matrix.component }}
        npm ci

    - name: Run linting
      run: |
        cd ${{ matrix.component }}
        npm run lint

    - name: Run tests
      run: |
        cd ${{ matrix.component }}
        npm test
      env:
        CI: true

    - name: Upload coverage reports
      if: matrix.component == 'backend'
      uses: codecov/codecov-action@v3
      with:
        file: ./backend/coverage/lcov.info
        flags: backend

  # Smart Contract Compilation and Verification
  contracts:
    runs-on: ubuntu-latest
    needs: test
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: contracts/package-lock.json

    - name: Install dependencies
      run: |
        cd contracts
        npm ci

    - name: Compile contracts
      run: |
        cd contracts
        npm run compile

    - name: Run contract tests
      run: |
        cd contracts
        npm test

    - name: Generate contract size report
      run: |
        cd contracts
        npm run size

    - name: Upload contract artifacts
      uses: actions/upload-artifact@v3
      with:
        name: contract-artifacts
        path: contracts/artifacts/

  # Build and Push Docker Images
  build:
    runs-on: ubuntu-latest
    needs: [test, contracts]
    if: github.event_name == 'push'
    
    permissions:
      contents: read
      packages: write

    strategy:
      matrix:
        component: [backend, frontend]

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-${{ matrix.component }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=semver,pattern={{version}}
          type=semver,pattern={{major}}.{{minor}}
          type=sha

    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        file: docker/${{ matrix.component }}.Dockerfile
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}

  # Deploy to Testnet
  deploy-testnet:
    runs-on: ubuntu-latest
    needs: [build]
    if: github.ref == 'refs/heads/develop'
    environment: testnet
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: contracts/package-lock.json

    - name: Install contract dependencies
      run: |
        cd contracts
        npm ci

    - name: Deploy to testnet
      run: |
        cd contracts
        npm run deploy:testnet
      env:
        PRIVATE_KEY: ${{ secrets.TESTNET_PRIVATE_KEY }}
        RPC_URL_TESTNET: ${{ secrets.RPC_URL_TESTNET }}
        CHAINLINK_SUBSCRIPTION_ID: ${{ secrets.CHAINLINK_SUBSCRIPTION_ID_TESTNET }}
        ETHERSCAN_API_KEY: ${{ secrets.ETHERSCAN_API_KEY }}

    - name: Upload deployment artifacts
      uses: actions/upload-artifact@v3
      with:
        name: testnet-deployment
        path: contracts/deployments/

  # Deploy to Mainnet
  deploy-mainnet:
    runs-on: ubuntu-latest
    needs: [build]
    if: github.event_name == 'release'
    environment: mainnet
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: contracts/package-lock.json

    - name: Install contract dependencies
      run: |
        cd contracts
        npm ci

    - name: Deploy to mainnet
      run: |
        cd contracts
        npm run deploy:mainnet
      env:
        PRIVATE_KEY: ${{ secrets.MAINNET_PRIVATE_KEY }}
        RPC_URL_MAINNET: ${{ secrets.RPC_URL_MAINNET }}
        CHAINLINK_SUBSCRIPTION_ID: ${{ secrets.CHAINLINK_SUBSCRIPTION_ID_MAINNET }}
        ETHERSCAN_API_KEY: ${{ secrets.ETHERSCAN_API_KEY }}

    - name: Upload deployment artifacts
      uses: actions/upload-artifact@v3
      with:
        name: mainnet-deployment
        path: contracts/deployments/

    - name: Create deployment summary
      run: |
        echo "## 🚀 Mainnet Deployment Complete" >> $GITHUB_STEP_SUMMARY
        echo "- **Release**: ${{ github.event.release.tag_name }}" >> $GITHUB_STEP_SUMMARY
        echo "- **Commit**: ${{ github.sha }}" >> $GITHUB_STEP_SUMMARY
        echo "- **Deployed by**: ${{ github.actor }}" >> $GITHUB_STEP_SUMMARY
        echo "- **Timestamp**: $(date -u)" >> $GITHUB_STEP_SUMMARY

  # Security Scan
  security:
    runs-on: ubuntu-latest
    needs: test
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'

    - name: Upload Trivy scan results
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: 'trivy-results.sarif'

    - name: Run npm audit
      run: |
        cd backend && npm audit --audit-level moderate
        cd ../frontend && npm audit --audit-level moderate
        cd ../contracts && npm audit --audit-level moderate
