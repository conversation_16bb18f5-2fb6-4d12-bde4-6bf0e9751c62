import React from 'react';
import { Link } from 'react-router-dom';

const NotFound = () => {
  return (
    <div className="container" style={{ 
      padding: '2rem 1rem',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      minHeight: '60vh'
    }}>
      <div className="text-center">
        <div style={{ fontSize: '6rem', marginBottom: '1rem' }}>🎰</div>
        <h1 style={{ fontSize: '3rem', marginBottom: '1rem' }}>404</h1>
        <h2 style={{ marginBottom: '1rem' }}>Page Not Found</h2>
        <p style={{ 
          opacity: 0.7, 
          marginBottom: '2rem',
          fontSize: '1.125rem',
          maxWidth: '500px',
          margin: '0 auto 2rem'
        }}>
          Looks like this page hit the "Lose" segment! 
          The page you're looking for doesn't exist or has been moved.
        </p>
        
        <div style={{ display: 'flex', gap: '1rem', justifyContent: 'center', flexWrap: 'wrap' }}>
          <Link to="/" className="btn btn-primary btn-lg">
            🎰 Back to Game
          </Link>
          <Link to="/stats" className="btn btn-secondary btn-lg">
            📊 View Stats
          </Link>
        </div>
        
        <div style={{ 
          marginTop: '3rem',
          padding: '1.5rem',
          background: 'rgba(255, 255, 255, 0.05)',
          borderRadius: '1rem',
          maxWidth: '400px',
          margin: '3rem auto 0'
        }}>
          <h3 style={{ marginBottom: '1rem' }}>Quick Links</h3>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
            <Link 
              to="/" 
              style={{ 
                color: 'rgba(255, 255, 255, 0.8)',
                textDecoration: 'none',
                padding: '0.5rem',
                borderRadius: '0.5rem',
                transition: 'background 0.2s ease'
              }}
              onMouseEnter={(e) => e.target.style.background = 'rgba(255, 255, 255, 0.1)'}
              onMouseLeave={(e) => e.target.style.background = 'transparent'}
            >
              🎮 Play Game
            </Link>
            <Link 
              to="/history" 
              style={{ 
                color: 'rgba(255, 255, 255, 0.8)',
                textDecoration: 'none',
                padding: '0.5rem',
                borderRadius: '0.5rem',
                transition: 'background 0.2s ease'
              }}
              onMouseEnter={(e) => e.target.style.background = 'rgba(255, 255, 255, 0.1)'}
              onMouseLeave={(e) => e.target.style.background = 'transparent'}
            >
              📊 View History
            </Link>
            <Link 
              to="/stats" 
              style={{ 
                color: 'rgba(255, 255, 255, 0.8)',
                textDecoration: 'none',
                padding: '0.5rem',
                borderRadius: '0.5rem',
                transition: 'background 0.2s ease'
              }}
              onMouseEnter={(e) => e.target.style.background = 'rgba(255, 255, 255, 0.1)'}
              onMouseLeave={(e) => e.target.style.background = 'transparent'}
            >
              📈 Game Statistics
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NotFound;
