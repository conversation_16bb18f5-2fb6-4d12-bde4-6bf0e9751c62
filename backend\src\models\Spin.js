const mongoose = require('mongoose');

const spinSchema = new mongoose.Schema({
  // Blockchain data
  requestId: {
    type: String,
    required: true,
    unique: true,
    index: true
  },
  transactionHash: {
    type: String,
    required: true,
    index: true
  },
  blockNumber: {
    type: Number,
    required: true
  },
  
  // Player data
  playerAddress: {
    type: String,
    required: true,
    lowercase: true,
    index: true
  },
  
  // Bet data
  betAmount: {
    type: String, // Store as string to avoid precision issues
    required: true
  },
  betAmountUSD: {
    type: Number,
    required: true
  },
  
  // Result data
  isResolved: {
    type: Boolean,
    default: false,
    index: true
  },
  segmentIndex: {
    type: Number,
    min: 0,
    max: 4
  },
  multiplier: {
    type: Number,
    min: 0
  },
  payout: {
    type: String, // Store as string to avoid precision issues
    default: '0'
  },
  payoutUSD: {
    type: Number,
    default: 0
  },
  
  // Metadata
  chainId: {
    type: Number,
    required: true
  },
  network: {
    type: String,
    required: true
  },
  
  // Timestamps
  placedAt: {
    type: Date,
    required: true,
    index: true
  },
  resolvedAt: {
    type: Date
  },
  
  // VRF data
  vrfRequestId: {
    type: String
  },
  randomWord: {
    type: String
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for efficient queries
spinSchema.index({ playerAddress: 1, placedAt: -1 });
spinSchema.index({ placedAt: -1 });
spinSchema.index({ isResolved: 1, placedAt: -1 });
spinSchema.index({ network: 1, placedAt: -1 });

// Virtual for profit/loss calculation
spinSchema.virtual('profitLoss').get(function() {
  if (!this.isResolved) return 0;
  return parseFloat(this.payout) - parseFloat(this.betAmount);
});

// Virtual for win status
spinSchema.virtual('isWin').get(function() {
  if (!this.isResolved) return null;
  return this.multiplier > 0;
});

// Static methods
spinSchema.statics.getPlayerStats = async function(playerAddress) {
  const stats = await this.aggregate([
    { $match: { playerAddress: playerAddress.toLowerCase(), isResolved: true } },
    {
      $group: {
        _id: null,
        totalSpins: { $sum: 1 },
        totalBet: { $sum: { $toDouble: '$betAmount' } },
        totalPayout: { $sum: { $toDouble: '$payout' } },
        wins: { $sum: { $cond: [{ $gt: ['$multiplier', 0] }, 1, 0] } },
        losses: { $sum: { $cond: [{ $eq: ['$multiplier', 0] }, 1, 0] } },
        biggestWin: { $max: { $toDouble: '$payout' } },
        biggestLoss: { $max: { $toDouble: '$betAmount' } }
      }
    }
  ]);

  if (stats.length === 0) {
    return {
      totalSpins: 0,
      totalBet: 0,
      totalPayout: 0,
      wins: 0,
      losses: 0,
      winRate: 0,
      profitLoss: 0,
      biggestWin: 0,
      biggestLoss: 0
    };
  }

  const stat = stats[0];
  return {
    ...stat,
    winRate: stat.totalSpins > 0 ? (stat.wins / stat.totalSpins) * 100 : 0,
    profitLoss: stat.totalPayout - stat.totalBet
  };
};

spinSchema.statics.getGlobalStats = async function() {
  const stats = await this.aggregate([
    { $match: { isResolved: true } },
    {
      $group: {
        _id: null,
        totalSpins: { $sum: 1 },
        totalVolume: { $sum: { $toDouble: '$betAmount' } },
        totalPayouts: { $sum: { $toDouble: '$payout' } },
        uniquePlayers: { $addToSet: '$playerAddress' },
        wins: { $sum: { $cond: [{ $gt: ['$multiplier', 0] }, 1, 0] } }
      }
    }
  ]);

  if (stats.length === 0) {
    return {
      totalSpins: 0,
      totalVolume: 0,
      totalPayouts: 0,
      uniquePlayers: 0,
      wins: 0,
      houseEdge: 0
    };
  }

  const stat = stats[0];
  return {
    ...stat,
    uniquePlayers: stat.uniquePlayers.length,
    houseEdge: stat.totalVolume > 0 ? ((stat.totalVolume - stat.totalPayouts) / stat.totalVolume) * 100 : 0
  };
};

// Instance methods
spinSchema.methods.resolve = function(segmentIndex, multiplier, payout, randomWord) {
  this.isResolved = true;
  this.segmentIndex = segmentIndex;
  this.multiplier = multiplier;
  this.payout = payout;
  this.payoutUSD = parseFloat(payout) / 1e6; // Convert from 6 decimals to USD
  this.resolvedAt = new Date();
  this.randomWord = randomWord;
  return this.save();
};

module.exports = mongoose.model('Spin', spinSchema);
