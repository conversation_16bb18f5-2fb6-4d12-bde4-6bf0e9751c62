# Spin-to-Win dApp Makefile

.PHONY: help install clean build test lint deploy dev prod stop logs

# Default target
help:
	@echo "Spin-to-Win dApp - Available Commands:"
	@echo ""
	@echo "Setup Commands:"
	@echo "  install     - Install all dependencies"
	@echo "  clean       - Clean all build artifacts and dependencies"
	@echo ""
	@echo "Development Commands:"
	@echo "  dev         - Start development environment"
	@echo "  dev-db      - Start only database services"
	@echo "  dev-stop    - Stop development environment"
	@echo "  dev-logs    - View development logs"
	@echo ""
	@echo "Build Commands:"
	@echo "  build       - Build all components"
	@echo "  build-contracts - Compile smart contracts"
	@echo "  build-backend   - Build backend"
	@echo "  build-frontend  - Build frontend"
	@echo ""
	@echo "Test Commands:"
	@echo "  test        - Run all tests"
	@echo "  test-contracts - Test smart contracts"
	@echo "  test-backend   - Test backend"
	@echo "  test-frontend  - Test frontend"
	@echo ""
	@echo "Lint Commands:"
	@echo "  lint        - Lint all code"
	@echo "  lint-fix    - Fix linting issues"
	@echo ""
	@echo "Deployment Commands:"
	@echo "  deploy-testnet - Deploy to testnet"
	@echo "  deploy-mainnet - Deploy to mainnet"
	@echo ""
	@echo "Production Commands:"
	@echo "  prod        - Start production environment"
	@echo "  prod-stop   - Stop production environment"
	@echo "  prod-logs   - View production logs"

# Setup Commands
install:
	@echo "Installing all dependencies..."
	npm install
	cd contracts && npm install
	cd backend && npm install
	cd frontend && npm install
	@echo "✅ All dependencies installed!"

clean:
	@echo "Cleaning build artifacts and dependencies..."
	rm -rf node_modules
	cd contracts && rm -rf node_modules artifacts cache coverage
	cd backend && rm -rf node_modules dist coverage logs
	cd frontend && rm -rf node_modules build coverage
	@echo "✅ Cleanup complete!"

# Development Commands
dev:
	@echo "Starting development environment..."
	docker-compose -f docker-compose.dev.yml up -d
	@echo "✅ Development environment started!"
	@echo "📊 MongoDB Express: http://localhost:8081 (admin/admin)"
	@echo "🔴 Redis Commander: http://localhost:8082"
	@echo "⛓️  Hardhat Node: http://localhost:8545"

dev-db:
	@echo "Starting database services..."
	docker-compose -f docker-compose.dev.yml up -d mongodb-dev redis-dev mongo-express redis-commander
	@echo "✅ Database services started!"

dev-stop:
	@echo "Stopping development environment..."
	docker-compose -f docker-compose.dev.yml down
	@echo "✅ Development environment stopped!"

dev-logs:
	docker-compose -f docker-compose.dev.yml logs -f

# Build Commands
build: build-contracts build-backend build-frontend

build-contracts:
	@echo "Compiling smart contracts..."
	cd contracts && npm run compile
	@echo "✅ Smart contracts compiled!"

build-backend:
	@echo "Building backend..."
	cd backend && npm run build
	@echo "✅ Backend built!"

build-frontend:
	@echo "Building frontend..."
	cd frontend && npm run build
	@echo "✅ Frontend built!"

# Test Commands
test: test-contracts test-backend test-frontend

test-contracts:
	@echo "Testing smart contracts..."
	cd contracts && npm test
	@echo "✅ Smart contract tests passed!"

test-backend:
	@echo "Testing backend..."
	cd backend && npm test
	@echo "✅ Backend tests passed!"

test-frontend:
	@echo "Testing frontend..."
	cd frontend && npm test -- --coverage --watchAll=false
	@echo "✅ Frontend tests passed!"

# Lint Commands
lint:
	@echo "Linting all code..."
	cd contracts && npm run lint
	cd backend && npm run lint
	cd frontend && npm run lint
	@echo "✅ Linting complete!"

lint-fix:
	@echo "Fixing linting issues..."
	cd contracts && npm run lint || true
	cd backend && npm run lint:fix
	cd frontend && npm run lint:fix
	@echo "✅ Linting fixes applied!"

# Deployment Commands
deploy-testnet:
	@echo "Deploying to testnet..."
	cd contracts && npm run deploy:testnet
	@echo "✅ Deployed to testnet!"

deploy-mainnet:
	@echo "⚠️  Deploying to MAINNET..."
	@read -p "Are you sure? This will deploy to mainnet! (y/N): " confirm && [ "$$confirm" = "y" ]
	cd contracts && npm run deploy:mainnet
	@echo "✅ Deployed to mainnet!"

# Production Commands
prod:
	@echo "Starting production environment..."
	docker-compose --profile production up -d
	@echo "✅ Production environment started!"

prod-stop:
	@echo "Stopping production environment..."
	docker-compose --profile production down
	@echo "✅ Production environment stopped!"

prod-logs:
	docker-compose logs -f

# Utility Commands
logs:
	docker-compose logs -f

stop:
	docker-compose down

restart: stop dev

# Database Commands
db-reset:
	@echo "Resetting development database..."
	docker-compose -f docker-compose.dev.yml down -v
	docker-compose -f docker-compose.dev.yml up -d mongodb-dev redis-dev
	@echo "✅ Database reset complete!"

# Contract Commands
compile: build-contracts

deploy: deploy-testnet

# Quick development setup
quick-start: install dev-db
	@echo "🚀 Quick start complete!"
	@echo "Next steps:"
	@echo "1. Copy .env.example to .env and configure"
	@echo "2. Run 'make deploy-testnet' to deploy contracts"
	@echo "3. Start backend: cd backend && npm run dev"
	@echo "4. Start frontend: cd frontend && npm start"
