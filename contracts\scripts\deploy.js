const { ethers, network } = require("hardhat");
require("dotenv").config();

// Network configurations
const networkConfigs = {
  mainnet: {
    vrfCoordinator: "******************************************",
    keyHash: "0x8af398995b04c28e9951adb9721ef74c74f93e6a478f39e7e0777be13527e7ef",
    usdtAddress: "******************************************",
  },
  sepolia: {
    vrfCoordinator: "******************************************",
    keyHash: "0x474e34a077df58807dbe9c96d3c009b23b3c6d0cce433e59bbf5b34f823bc56c",
    usdtAddress: "******************************************", // Mock USDT for testnet
  },
  bsc: {
    vrfCoordinator: "******************************************",
    keyHash: "0x17cd473250a9a479dc7f234c64332ed4bc8af9e8ded7556aa6e66d83da49f470",
    usdtAddress: "******************************************",
  },
  bscTestnet: {
    vrfCoordinator: "******************************************",
    keyHash: "0xd4bb89654db74673a187bd804519e65e3f71a52bc55f11da7601a13dcf505314",
    usdtAddress: "******************************************", // Mock USDT for testnet
  },
};

async function main() {
  console.log(`Deploying to ${network.name}...`);
  
  const [deployer] = await ethers.getSigners();
  console.log("Deploying contracts with account:", deployer.address);
  
  const balance = await deployer.getBalance();
  console.log("Account balance:", ethers.utils.formatEther(balance), "ETH");

  // Get network configuration
  const config = networkConfigs[network.name];
  if (!config) {
    throw new Error(`Network ${network.name} not configured`);
  }

  // Get subscription ID from environment
  const subscriptionId = process.env.CHAINLINK_SUBSCRIPTION_ID;
  if (!subscriptionId) {
    throw new Error("CHAINLINK_SUBSCRIPTION_ID not set in environment");
  }

  console.log("Network config:", config);
  console.log("Subscription ID:", subscriptionId);

  // Deploy SpinToWin contract
  const SpinToWin = await ethers.getContractFactory("SpinToWin");
  const spinToWin = await SpinToWin.deploy(
    config.vrfCoordinator,
    subscriptionId,
    config.keyHash,
    config.usdtAddress
  );

  await spinToWin.deployed();

  console.log("SpinToWin deployed to:", spinToWin.address);
  console.log("Transaction hash:", spinToWin.deployTransaction.hash);

  // Wait for a few confirmations
  console.log("Waiting for confirmations...");
  await spinToWin.deployTransaction.wait(3);

  // Verify contract on Etherscan/BSCScan
  if (network.name !== "hardhat" && network.name !== "localhost") {
    console.log("Verifying contract...");
    try {
      await hre.run("verify:verify", {
        address: spinToWin.address,
        constructorArguments: [
          config.vrfCoordinator,
          subscriptionId,
          config.keyHash,
          config.usdtAddress,
        ],
      });
      console.log("Contract verified successfully");
    } catch (error) {
      console.log("Verification failed:", error.message);
    }
  }

  // Save deployment info
  const deploymentInfo = {
    network: network.name,
    chainId: network.config.chainId,
    contractAddress: spinToWin.address,
    deployerAddress: deployer.address,
    transactionHash: spinToWin.deployTransaction.hash,
    blockNumber: spinToWin.deployTransaction.blockNumber,
    gasUsed: spinToWin.deployTransaction.gasLimit.toString(),
    timestamp: new Date().toISOString(),
    constructorArgs: {
      vrfCoordinator: config.vrfCoordinator,
      subscriptionId: subscriptionId,
      keyHash: config.keyHash,
      usdtAddress: config.usdtAddress,
    },
  };

  // Write deployment info to file
  const fs = require("fs");
  const path = require("path");
  const deploymentsDir = path.join(__dirname, "../deployments");
  
  if (!fs.existsSync(deploymentsDir)) {
    fs.mkdirSync(deploymentsDir, { recursive: true });
  }

  const deploymentFile = path.join(deploymentsDir, `${network.name}.json`);
  fs.writeFileSync(deploymentFile, JSON.stringify(deploymentInfo, null, 2));

  console.log("Deployment info saved to:", deploymentFile);
  console.log("\nDeployment Summary:");
  console.log("==================");
  console.log("Contract Address:", spinToWin.address);
  console.log("Network:", network.name);
  console.log("Deployer:", deployer.address);
  console.log("VRF Coordinator:", config.vrfCoordinator);
  console.log("USDT Address:", config.usdtAddress);
  console.log("Subscription ID:", subscriptionId);

  console.log("\nNext steps:");
  console.log("1. Add the contract as a consumer to your Chainlink VRF subscription");
  console.log("2. Fund the contract with USDT for payouts");
  console.log("3. Update your backend and frontend with the contract address");
  console.log("4. Test the contract with small bets first");
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
